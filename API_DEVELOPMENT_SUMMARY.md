# 拍照搜题API开发总结文档

## 项目概述

本项目是一个基于Go语言开发的拍照搜题API服务，采用微服务架构设计，提供完整的题目识别、解析和管理功能。系统支持高并发访问，具备完善的用户管理、应用管理、计费系统和统计分析功能。

## 技术架构

### 核心技术栈
- **后端框架**: <PERSON>in (Go 1.19+)
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **AI模型**: 通义千问VL、Deepseek Chat
- **短信服务**: 阿里云SMS
- **日志系统**: Zap + Lumberjack

### 架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │────│   API网关       │────│   业务服务      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   认证鉴权      │    │   AI模型服务    │
                       └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   MySQL数据库   │    │   Redis缓存     │
                       └─────────────────┘    └─────────────────┘
```

## 功能模块详解

### 1. 用户管理模块
**功能**: 完整的用户生命周期管理
- 用户注册（手机号+验证码）
- 用户登录（手机号+密码）
- 密码管理（修改密码、忘记密码）
- 用户资料管理

**核心接口**:
- `POST /api/v1/user/register` - 用户注册
- `POST /api/v1/user/login` - 用户登录
- `PUT /api/v1/user/{user_id}/change-password` - 修改密码

### 2. 应用管理模块
**功能**: 多应用支持，每个应用独立API密钥
- 应用创建和配置
- API密钥生成和管理
- 应用状态控制
- 应用使用统计

**核心接口**:
- `POST /api/v1/user/{user_id}/app` - 创建应用
- `GET /api/v1/user/{user_id}/app` - 获取应用列表
- `PUT /api/v1/user/{user_id}/app/{app_id}/regenerate-keys` - 重新生成密钥

### 3. 拍照搜题模块
**功能**: 核心业务功能，图片识别和题目解析
- 图片URL验证和处理
- AI模型调用（通义千问VL识别，Deepseek解析）
- 题目缓存机制
- 结果格式化输出

**核心接口**:
- `POST /api/v1/api/search` - 拍照搜题（需要API密钥认证）

**处理流程**:
1. 验证API密钥和用户权限
2. 检查用户余额是否足够
3. 验证图片URL格式
4. 查询Redis缓存
5. 调用AI模型识别和解析
6. 保存结果到数据库和缓存
7. 扣费并记录日志

### 4. 计费系统模块
**功能**: 灵活的计费策略和余额管理
- 价格配置管理（默认价格+用户定制价格）
- 余额充值和扣费
- 余额变动日志
- 余额不足保护

**核心接口**:
- `POST /api/v1/user/{user_id}/recharge` - 用户充值
- `GET /api/v1/user/{user_id}/balance` - 查询余额
- `GET /api/v1/user/{user_id}/balance/logs` - 余额日志

### 5. 认证鉴权模块
**功能**: 多层级安全认证
- API密钥认证（X-App-Key + X-Secret-Key）
- 用户身份验证
- 应用类型识别
- 权限控制

**中间件**:
- `APIAuth()` - API密钥认证
- `RateLimit()` - 限流控制
- `BalanceCheck()` - 余额检查

### 6. 限流控制模块
**功能**: 多维度限流保护
- IP级别限流（防止恶意攻击）
- 用户级别限流（防止滥用）
- 应用级别限流（按应用配置）
- Redis分布式限流

**限流策略**:
- IP限流: 每分钟100次请求
- 用户限流: 根据配置动态调整
- 应用限流: 按应用类型配置

### 7. 统计分析模块
**功能**: 详细的数据统计和分析
- API调用统计
- 用户行为分析
- 系统性能监控
- 收入统计分析

**核心接口**:
- `GET /api/v1/admin/stats/system` - 系统统计
- `GET /api/v1/admin/stats/user/{user_id}` - 用户统计
- `GET /api/v1/admin/logs/api` - API调用日志

### 8. 系统管理模块
**功能**: 后台管理和系统维护
- 模型配置管理
- 价格配置管理
- 系统健康检查
- 数据备份和清理

## 数据库设计

### 核心表结构
```sql
-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    phone VARCHAR(20) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    balance DECIMAL(10,2) DEFAULT 0.00,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 应用表
CREATE TABLE applications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    type TINYINT NOT NULL,
    app_key VARCHAR(64) UNIQUE NOT NULL,
    secret_key VARCHAR(64) NOT NULL,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 题目表
CREATE TABLE questions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    hash VARCHAR(64) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    analysis TEXT,
    answer TEXT,
    subject VARCHAR(20),
    grade VARCHAR(20),
    difficulty TINYINT,
    source_model VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 价格配置表
CREATE TABLE price_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    service_type TINYINT NOT NULL,
    user_id INT DEFAULT 0,
    price DECIMAL(8,4) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 余额日志表
CREATE TABLE balance_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    balance DECIMAL(10,2) NOT NULL,
    type TINYINT NOT NULL,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API认证机制

### 1. API密钥认证
```http
POST /api/v1/api/search
X-App-Key: your_app_key
X-Secret-Key: your_secret_key
Content-Type: application/json

{
    "image_url": "https://example.com/question.jpg"
}
```

### 2. 认证流程
1. 提取请求头中的API密钥
2. 验证密钥格式和有效性
3. 查询应用信息和用户信息
4. 检查应用状态和用户状态
5. 设置上下文信息供后续中间件使用

## 缓存策略

### Redis缓存设计
- **题目缓存**: `question:{hash}` - TTL: 7天
- **限流缓存**: `rate_limit:{type}:{key}` - TTL: 动态
- **用户会话**: `session:{user_id}` - TTL: 24小时

### 缓存更新策略
- **写入策略**: 先写数据库，再更新缓存
- **失效策略**: TTL自动过期 + 手动清理
- **一致性**: 最终一致性，允许短暂不一致

## 性能优化

### 1. 数据库优化
- 合理的索引设计
- 连接池配置
- 查询优化
- 读写分离（可扩展）

### 2. 缓存优化
- 热点数据缓存
- 查询结果缓存
- 分布式缓存
- 缓存预热

### 3. 并发优化
- Goroutine池
- 连接复用
- 异步处理
- 限流保护

## 监控和日志

### 日志系统
- **访问日志**: 记录所有API调用
- **错误日志**: 记录系统错误和异常
- **业务日志**: 记录关键业务操作
- **性能日志**: 记录响应时间和资源使用

### 监控指标
- **QPS**: 每秒请求数
- **响应时间**: 平均响应时间和P99
- **错误率**: 4xx和5xx错误比例
- **缓存命中率**: Redis缓存命中率

## 安全措施

### 1. 认证安全
- API密钥加密存储
- 密钥定期轮换
- 请求签名验证

### 2. 数据安全
- 密码哈希存储
- 敏感数据加密
- SQL注入防护

### 3. 访问控制
- 限流保护
- IP白名单
- 权限控制

## 部署架构

### 生产环境部署
- **负载均衡**: Nginx反向代理
- **应用服务**: 多实例部署
- **数据库**: MySQL主从复制
- **缓存**: Redis集群
- **监控**: 日志收集和监控告警

### 扩展性设计
- **水平扩展**: 支持多实例部署
- **垂直扩展**: 支持资源升级
- **微服务化**: 模块化设计便于拆分
- **云原生**: 支持容器化部署

## 开发规范

### 代码规范
- 遵循Go语言官方规范
- 统一的错误处理
- 完善的注释文档
- 单元测试覆盖

### API规范
- RESTful API设计
- 统一的响应格式
- 标准的HTTP状态码
- 详细的错误信息

## 总结

本项目成功实现了一个完整的拍照搜题API服务，具备以下特点：

1. **功能完整**: 涵盖用户管理、应用管理、搜题服务、计费系统等全部功能
2. **架构合理**: 采用分层架构，模块化设计，易于维护和扩展
3. **性能优秀**: 通过缓存、限流、优化等手段保证高性能
4. **安全可靠**: 多层安全防护，完善的错误处理和监控
5. **易于部署**: 提供完整的部署文档和脚本

项目已具备生产环境部署条件，可以支持大规模用户访问和业务扩展。
