#!/bin/bash

# 密码管理API测试脚本
# 测试用户和管理员的密码管理功能

BASE_URL="http://localhost:8080"
USER_ID="1"
ADMIN_ID="1"
PHONE="15653259315"
ADMIN_USERNAME="15688515913"

echo "=== 密码管理API测试 ==="
echo "基础URL: $BASE_URL"
echo "测试用户ID: $USER_ID"
echo "测试管理员ID: $ADMIN_ID"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    
    echo -e "${YELLOW}测试: $description${NC}"
    echo "请求: $method $url"
    if [ ! -z "$data" ]; then
        echo "数据: $data"
    fi
    
    if [ ! -z "$data" ]; then
        response=$(curl -s -X $method "$url" \
            -H "Content-Type: application/json" \
            -d "$data")
    else
        response=$(curl -s -X $method "$url")
    fi
    
    echo "响应: $response"
    
    # 检查响应是否包含成功标识
    if echo "$response" | grep -q '"code":200'; then
        echo -e "${GREEN}✓ 测试通过${NC}"
    else
        echo -e "${RED}✗ 测试失败${NC}"
    fi
    echo "----------------------------------------"
}

echo -e "${BLUE}=== 用户密码管理测试 ===${NC}"

# 1. 用户修改密码（使用原密码）
echo -e "${YELLOW}1. 用户修改密码测试${NC}"
test_api "PUT" "$BASE_URL/api/v1/user/$USER_ID/change-password" \
    '{"old_password":"123456","new_password":"newpassword123"}' \
    "用户使用原密码修改密码"

# 2. 用户发送忘记密码验证码
echo -e "${YELLOW}2. 用户忘记密码验证码测试${NC}"
test_api "POST" "$BASE_URL/api/v1/user/forgot-password" \
    "{\"phone\":\"$PHONE\"}" \
    "发送忘记密码验证码"

# 3. 用户重置密码（使用验证码）
echo -e "${YELLOW}3. 用户重置密码测试${NC}"
test_api "POST" "$BASE_URL/api/v1/user/reset-password" \
    "{\"phone\":\"$PHONE\",\"code\":\"123456\",\"new_password\":\"resetpassword123\"}" \
    "使用验证码重置密码"

# 4. 测试用新密码登录
echo -e "${YELLOW}4. 验证新密码登录${NC}"
test_api "POST" "$BASE_URL/api/v1/user/login" \
    "{\"phone\":\"$PHONE\",\"password\":\"resetpassword123\"}" \
    "使用重置后的密码登录"

echo ""
echo -e "${BLUE}=== 管理员密码管理测试 ===${NC}"

# 5. 管理员登录
echo -e "${YELLOW}5. 管理员登录测试${NC}"
test_api "POST" "$BASE_URL/api/v1/admin/login" \
    "{\"username\":\"$ADMIN_USERNAME\",\"password\":\"admin888\"}" \
    "管理员登录"

# 6. 管理员修改密码（使用原密码）
echo -e "${YELLOW}6. 管理员修改密码测试${NC}"
test_api "PUT" "$BASE_URL/api/v1/admin/$ADMIN_ID/change-password" \
    '{"old_password":"admin888","new_password":"newadmin123"}' \
    "管理员使用原密码修改密码"

# 7. 管理员发送忘记密码验证码
echo -e "${YELLOW}7. 管理员忘记密码验证码测试${NC}"
test_api "POST" "$BASE_URL/api/v1/admin/forgot-password" \
    "{\"username\":\"$ADMIN_USERNAME\"}" \
    "管理员发送忘记密码验证码"

# 8. 管理员重置密码（使用验证码）
echo -e "${YELLOW}8. 管理员重置密码测试${NC}"
test_api "POST" "$BASE_URL/api/v1/admin/reset-password" \
    "{\"username\":\"$ADMIN_USERNAME\",\"code\":\"123456\",\"new_password\":\"resetadmin123\"}" \
    "管理员使用验证码重置密码"

# 9. 管理员重置用户密码
echo -e "${YELLOW}9. 管理员重置用户密码测试${NC}"
test_api "PUT" "$BASE_URL/api/v1/admin/$ADMIN_ID/user/$USER_ID/reset-password" \
    '{"new_password":"adminreset123"}' \
    "管理员重置用户密码"

echo ""
echo -e "${BLUE}=== 错误情况测试 ===${NC}"

# 10. 错误的原密码
echo -e "${YELLOW}10. 错误原密码测试${NC}"
test_api "PUT" "$BASE_URL/api/v1/user/$USER_ID/change-password" \
    '{"old_password":"wrongpassword","new_password":"newpassword123"}' \
    "使用错误的原密码修改"

# 11. 无效的验证码
echo -e "${YELLOW}11. 无效验证码测试${NC}"
test_api "POST" "$BASE_URL/api/v1/user/reset-password" \
    "{\"phone\":\"$PHONE\",\"code\":\"000000\",\"new_password\":\"newpassword123\"}" \
    "使用无效验证码重置密码"

# 12. 不存在的手机号
echo -e "${YELLOW}12. 不存在手机号测试${NC}"
test_api "POST" "$BASE_URL/api/v1/user/forgot-password" \
    '{"phone":"19999999999"}' \
    "向不存在的手机号发送验证码"

# 13. 不存在的管理员
echo -e "${YELLOW}13. 不存在管理员测试${NC}"
test_api "POST" "$BASE_URL/api/v1/admin/login" \
    '{"username":"nonexistent","password":"password"}' \
    "不存在的管理员登录"

# 14. 密码格式错误
echo -e "${YELLOW}14. 密码格式错误测试${NC}"
test_api "PUT" "$BASE_URL/api/v1/user/$USER_ID/change-password" \
    '{"old_password":"adminreset123","new_password":"123"}' \
    "新密码长度不足"

# 15. 普通管理员重置用户密码（权限测试）
echo -e "${YELLOW}15. 权限测试${NC}"
test_api "PUT" "$BASE_URL/api/v1/admin/2/user/$USER_ID/reset-password" \
    '{"new_password":"unauthorized123"}' \
    "普通管理员尝试重置用户密码（应该失败）"

echo ""
echo -e "${GREEN}=== 密码管理API测试完成 ===${NC}"
echo ""
echo "注意事项："
echo "1. 确保服务器正在运行在 $BASE_URL"
echo "2. 确保用户ID $USER_ID 和管理员ID $ADMIN_ID 存在"
echo "3. 某些测试可能会因为数据库状态而失败"
echo "4. 验证码在Redis不可用时使用固定值 123456"
echo "5. 密码修改后请记住新密码用于后续测试"
