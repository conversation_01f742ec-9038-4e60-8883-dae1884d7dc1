<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志管理系统 - Solve API</title>
    <link rel="stylesheet" href="/static/css/log-viewer.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 头部导航 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="bi bi-file-text"></i> 日志管理系统
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text">
                        <i class="bi bi-clock"></i> <span id="current-time"></span>
                    </span>
                </div>
            </div>
        </nav>

        <!-- 主要内容 -->
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-funnel"></i> 日志类型</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action active" data-log-type="api">
                                <i class="bi bi-cloud"></i> API日志
                                <span class="badge bg-primary rounded-pill" id="api-log-count">0</span>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" data-log-type="system">
                                <i class="bi bi-gear"></i> 系统日志
                                <span class="badge bg-secondary rounded-pill" id="system-log-count">0</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 过滤器 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5><i class="bi bi-filter"></i> 过滤条件</h5>
                    </div>
                    <div class="card-body">
                        <form id="filter-form">
                            <!-- API日志过滤器 -->
                            <div id="api-filters">
                                <div class="mb-3">
                                    <label class="form-label">用户ID</label>
                                    <input type="number" class="form-control" name="user_id" placeholder="输入用户ID">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">应用ID</label>
                                    <input type="number" class="form-control" name="app_id" placeholder="输入应用ID">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">请求方法</label>
                                    <select class="form-select" name="method">
                                        <option value="">全部</option>
                                        <option value="GET">GET</option>
                                        <option value="POST">POST</option>
                                        <option value="PUT">PUT</option>
                                        <option value="DELETE">DELETE</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">状态码</label>
                                    <select class="form-select" name="status_code">
                                        <option value="">全部</option>
                                        <option value="200">200 成功</option>
                                        <option value="400">400 请求错误</option>
                                        <option value="401">401 未授权</option>
                                        <option value="404">404 未找到</option>
                                        <option value="500">500 服务器错误</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">请求路径</label>
                                    <input type="text" class="form-control" name="path" placeholder="输入路径关键词">
                                </div>
                            </div>

                            <!-- 系统日志过滤器 -->
                            <div id="system-filters" style="display: none;">
                                <div class="mb-3">
                                    <label class="form-label">日志级别</label>
                                    <select class="form-select" name="level">
                                        <option value="">全部</option>
                                        <option value="INFO">INFO</option>
                                        <option value="WARN">WARN</option>
                                        <option value="ERROR">ERROR</option>
                                        <option value="DEBUG">DEBUG</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">关键词</label>
                                    <input type="text" class="form-control" name="keyword" placeholder="输入关键词">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">显示行数</label>
                                    <select class="form-select" name="lines">
                                        <option value="100">100行</option>
                                        <option value="200">200行</option>
                                        <option value="500">500行</option>
                                        <option value="1000">1000行</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 通用时间过滤器 -->
                            <div class="mb-3">
                                <label class="form-label">开始日期</label>
                                <input type="date" class="form-control" name="start_date">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">结束日期</label>
                                <input type="date" class="form-control" name="end_date">
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i> 查询
                                </button>
                                <button type="button" class="btn btn-secondary" id="reset-filters">
                                    <i class="bi bi-arrow-clockwise"></i> 重置
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5><i class="bi bi-tools"></i> 操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" id="export-logs">
                                <i class="bi bi-download"></i> 导出日志
                            </button>
                            <button class="btn btn-warning" id="refresh-logs">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                            <button class="btn btn-danger" id="clean-logs">
                                <i class="bi bi-trash"></i> 清理日志
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9">
                <!-- 统计信息 -->
                <div class="row mb-4" id="stats-section">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary">总调用</h5>
                                <h3 class="card-text" id="total-calls">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success">成功</h5>
                                <h3 class="card-text" id="success-calls">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger">错误</h5>
                                <h3 class="card-text" id="error-calls">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info">成功率</h5>
                                <h3 class="card-text" id="success-rate">0%</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日志列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-list"></i> 
                            <span id="log-type-title">API日志</span>
                        </h5>
                        <div>
                            <span class="badge bg-info" id="log-count">共 0 条</span>
                            <button class="btn btn-sm btn-outline-primary" id="auto-refresh-toggle">
                                <i class="bi bi-arrow-repeat"></i> 自动刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 加载指示器 -->
                        <div id="loading" class="text-center" style="display: none;">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载日志...</p>
                        </div>

                        <!-- API日志表格 -->
                        <div id="api-logs-table" class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>时间</th>
                                        <th>用户ID</th>
                                        <th>应用ID</th>
                                        <th>方法</th>
                                        <th>路径</th>
                                        <th>状态码</th>
                                        <th>响应时间</th>
                                        <th>费用</th>
                                        <th>IP</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="api-logs-tbody">
                                    <!-- 动态加载 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 系统日志显示 -->
                        <div id="system-logs-display" style="display: none;">
                            <pre id="system-logs-content" class="bg-dark text-light p-3 rounded" style="height: 600px; overflow-y: auto;"></pre>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="日志分页" id="pagination-nav">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 动态生成分页 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 日志详情模态框 -->
    <div class="modal fade" id="logDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">日志详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="log-detail-content">
                        <!-- 动态加载日志详情 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 清理日志模态框 -->
    <div class="modal fade" id="cleanLogsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">清理日志</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="clean-logs-form">
                        <div class="mb-3">
                            <label class="form-label">保留天数</label>
                            <input type="number" class="form-control" name="days" value="30" min="1" max="365" required>
                            <div class="form-text">将删除指定天数之前的日志，建议保留30天</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirm-clean">确认清理</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/log-viewer.js"></script>
</body>
</html>
