<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拍照搜题API测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5a6fd8;
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #667eea;
        }

        .loading.show {
            display: block;
        }

        .result {
            display: none;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .result.show {
            display: block;
        }

        .error {
            display: none;
            margin-top: 20px;
            padding: 15px;
            background: #f8d7da;
            color: #721c24;
            border-radius: 6px;
        }

        .error.show {
            display: block;
        }

        .question-info {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .tag {
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: #495057;
        }

        .section {
            margin-bottom: 20px;
        }

        .section h4 {
            margin-bottom: 10px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }

        .options {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }

        .option {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .option:last-child {
            border-bottom: none;
        }

        .analysis {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            line-height: 1.6;
        }

        .meta {
            text-align: right;
            color: #6c757d;
            font-size: 12px;
            margin-top: 15px;
        }

        .cache-hit {
            color: #28a745;
            font-weight: bold;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
            
            .question-info {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 拍照搜题API测试</h1>
            <p>输入API密钥和图片URL，测试拍照搜题功能</p>
        </div>

        <div class="content">
            <form id="searchForm">
                <div class="two-column">
                    <div class="form-group">
                        <label for="appKey">App Key:</label>
                        <input type="text" id="appKey" placeholder="请输入App Key" required>
                    </div>
                    <div class="form-group">
                        <label for="secretKey">Secret Key:</label>
                        <input type="text" id="secretKey" placeholder="请输入Secret Key" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="authMethod">认证方式:</label>
                    <select id="authMethod" style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 6px;">
                        <option value="header">请求头认证（推荐）</option>
                        <option value="body">请求体认证（简化集成）</option>
                        <option value="auth">嵌套认证（结构化）</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="imageUrl">图片URL:</label>
                    <input type="url" id="imageUrl" placeholder="请输入图片URL" required>
                </div>

                <button type="submit" class="btn" id="searchBtn">开始搜题</button>
            </form>

            <div class="loading" id="loading">
                <p>🤖 AI正在识别和解析题目，请稍候...</p>
            </div>

            <div class="error" id="error"></div>

            <div class="result" id="result">
                <div class="question-info" id="questionInfo"></div>
                
                <div class="section">
                    <h4>📝 题目内容</h4>
                    <p id="questionText"></p>
                </div>

                <div class="section" id="optionsSection" style="display: none;">
                    <h4>📋 选项</h4>
                    <div class="options" id="options"></div>
                </div>

                <div class="section">
                    <h4>✅ 答案</h4>
                    <p id="answer"></p>
                </div>

                <div class="section">
                    <h4>💡 解析</h4>
                    <div class="analysis" id="analysis"></div>
                </div>

                <div class="meta" id="meta"></div>
            </div>
        </div>
    </div>

    <script>
        const form = document.getElementById('searchForm');
        const loading = document.getElementById('loading');
        const error = document.getElementById('error');
        const result = document.getElementById('result');
        const searchBtn = document.getElementById('searchBtn');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            const appKey = document.getElementById('appKey').value;
            const secretKey = document.getElementById('secretKey').value;
            const imageUrl = document.getElementById('imageUrl').value;
            const authMethod = document.getElementById('authMethod').value;

            // 重置状态
            loading.classList.remove('show');
            error.classList.remove('show');
            result.classList.remove('show');
            searchBtn.disabled = true;
            searchBtn.textContent = '搜题中...';

            // 显示加载状态
            loading.classList.add('show');

            try {
                let requestOptions = {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };

                let requestBody = {};

                // 根据认证方式构建请求
                switch (authMethod) {
                    case 'header':
                        requestOptions.headers['X-App-Key'] = appKey;
                        requestOptions.headers['X-Secret-Key'] = secretKey;
                        requestBody = { image_url: imageUrl };
                        break;

                    case 'body':
                        requestBody = {
                            app_key: appKey,
                            secret_key: secretKey,
                            image_url: imageUrl
                        };
                        break;

                    case 'auth':
                        requestBody = {
                            auth: {
                                app_key: appKey,
                                secret_key: secretKey
                            },
                            image_url: imageUrl
                        };
                        break;
                }

                requestOptions.body = JSON.stringify(requestBody);

                const response = await fetch('/api/v1/api/search', requestOptions);
                const data = await response.json();

                if (data.code === 200) {
                    displayResult(data.data, authMethod);
                } else {
                    showError(`错误 ${data.code}: ${data.message}`);
                }
            } catch (err) {
                showError('请求失败，请检查网络连接和服务器状态');
                console.error('Request failed:', err);
            } finally {
                loading.classList.remove('show');
                searchBtn.disabled = false;
                searchBtn.textContent = '开始搜题';
            }
        });

        function displayResult(data, authMethod) {
            // 显示题目信息标签
            const questionInfo = document.getElementById('questionInfo');
            questionInfo.innerHTML = `
                <span class="tag">📚 ${data.question_type || '未知类型'}</span>
                <span class="tag">📖 ${data.subject || '未知学科'}</span>
                <span class="tag">🎓 ${data.grade || '未知年级'}</span>
                <span class="tag">⭐ 难度 ${data.difficulty || 0}/5</span>
            `;

            // 显示题目内容
            document.getElementById('questionText').textContent = data.question_text || data.content || '无题目内容';

            // 显示选项（如果有）
            const optionsSection = document.getElementById('optionsSection');
            const optionsDiv = document.getElementById('options');
            
            if (data.options && Object.keys(data.options).length > 0) {
                optionsSection.style.display = 'block';
                optionsDiv.innerHTML = Object.entries(data.options)
                    .map(([key, value]) => `<div class="option"><strong>${key}.</strong> ${value}</div>`)
                    .join('');
            } else {
                optionsSection.style.display = 'none';
            }

            // 显示答案
            document.getElementById('answer').textContent = data.answer || '无答案';

            // 显示解析
            document.getElementById('analysis').textContent = data.analysis || '无解析';

            // 显示元信息
            const meta = document.getElementById('meta');
            const authMethodNames = {
                'header': '请求头认证',
                'body': '请求体认证',
                'auth': '嵌套认证'
            };
            meta.innerHTML = `
                处理时间: ${data.process_time || 0}ms |
                模型: ${data.source_model || '未知'} |
                认证方式: ${authMethodNames[authMethod] || '未知'} |
                ${data.cache_hit ? '<span class="cache-hit">缓存命中 ⚡</span>' : '实时解析 🔥'}
            `;

            result.classList.add('show');
        }

        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.classList.add('show');
        }

        // 页面加载时填入示例数据
        window.addEventListener('load', () => {
            document.getElementById('imageUrl').value = 'https://example.com/question.jpg';
        });
    </script>
</body>
</html>
