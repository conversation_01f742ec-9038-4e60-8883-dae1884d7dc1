#!/bin/bash

# 拍照搜题API服务启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
APP_NAME="solve_api"
CONFIG_FILE="config/config.yaml"
LOG_DIR="logs"
PID_FILE="$APP_NAME.pid"

# 显示帮助信息
show_help() {
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start     启动服务"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  status    查看服务状态"
    echo "  logs      查看日志"
    echo "  help      显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  CONFIG_FILE   配置文件路径 (默认: config/config.yaml)"
    echo "  LOG_LEVEL     日志级别 (debug/info/warn/error)"
    echo ""
}

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}检查依赖...${NC}"
    
    # 检查Go环境
    if ! command -v go &> /dev/null; then
        echo -e "${RED}错误: Go未安装或不在PATH中${NC}"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        echo -e "${RED}错误: 配置文件 $CONFIG_FILE 不存在${NC}"
        exit 1
    fi
    
    # 创建日志目录
    mkdir -p "$LOG_DIR"
    
    echo -e "${GREEN}依赖检查完成${NC}"
}

# 启动服务
start_service() {
    echo -e "${BLUE}启动 $APP_NAME 服务...${NC}"
    
    # 检查是否已经运行
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            echo -e "${YELLOW}服务已经在运行 (PID: $PID)${NC}"
            return 0
        else
            echo -e "${YELLOW}删除过期的PID文件${NC}"
            rm -f "$PID_FILE"
        fi
    fi
    
    # 检查依赖
    check_dependencies
    
    # 构建应用
    echo -e "${BLUE}构建应用...${NC}"
    go build -o "$APP_NAME" cmd/main.go
    
    # 启动服务
    echo -e "${BLUE}启动服务...${NC}"
    nohup ./"$APP_NAME" > "$LOG_DIR/app.out" 2>&1 &
    PID=$!
    
    # 保存PID
    echo "$PID" > "$PID_FILE"
    
    # 等待服务启动
    sleep 2
    
    # 检查服务状态
    if ps -p "$PID" > /dev/null 2>&1; then
        echo -e "${GREEN}服务启动成功 (PID: $PID)${NC}"
        
        # 检查健康状态
        echo -e "${BLUE}检查服务健康状态...${NC}"
        sleep 3
        if curl -s http://localhost:8080/health > /dev/null; then
            echo -e "${GREEN}服务健康检查通过${NC}"
        else
            echo -e "${YELLOW}警告: 服务健康检查失败，请检查日志${NC}"
        fi
    else
        echo -e "${RED}服务启动失败${NC}"
        rm -f "$PID_FILE"
        exit 1
    fi
}

# 停止服务
stop_service() {
    echo -e "${BLUE}停止 $APP_NAME 服务...${NC}"
    
    if [ ! -f "$PID_FILE" ]; then
        echo -e "${YELLOW}服务未运行${NC}"
        return 0
    fi
    
    PID=$(cat "$PID_FILE")
    
    if ps -p "$PID" > /dev/null 2>&1; then
        echo -e "${BLUE}发送停止信号 (PID: $PID)${NC}"
        kill "$PID"
        
        # 等待进程结束
        for i in {1..10}; do
            if ! ps -p "$PID" > /dev/null 2>&1; then
                break
            fi
            sleep 1
        done
        
        # 强制杀死进程
        if ps -p "$PID" > /dev/null 2>&1; then
            echo -e "${YELLOW}强制停止进程${NC}"
            kill -9 "$PID"
        fi
        
        echo -e "${GREEN}服务已停止${NC}"
    else
        echo -e "${YELLOW}进程不存在${NC}"
    fi
    
    rm -f "$PID_FILE"
}

# 重启服务
restart_service() {
    echo -e "${BLUE}重启 $APP_NAME 服务...${NC}"
    stop_service
    sleep 2
    start_service
}

# 查看服务状态
show_status() {
    echo -e "${BLUE}$APP_NAME 服务状态:${NC}"
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            echo -e "${GREEN}状态: 运行中${NC}"
            echo -e "PID: $PID"
            echo -e "启动时间: $(ps -o lstart= -p "$PID")"
            echo -e "内存使用: $(ps -o rss= -p "$PID" | awk '{print $1/1024 " MB"}')"
            
            # 检查健康状态
            if curl -s http://localhost:8080/health > /dev/null; then
                echo -e "${GREEN}健康状态: 正常${NC}"
            else
                echo -e "${RED}健康状态: 异常${NC}"
            fi
        else
            echo -e "${RED}状态: 已停止 (PID文件存在但进程不存在)${NC}"
            rm -f "$PID_FILE"
        fi
    else
        echo -e "${YELLOW}状态: 未运行${NC}"
    fi
}

# 查看日志
show_logs() {
    echo -e "${BLUE}查看 $APP_NAME 日志:${NC}"
    
    if [ -f "$LOG_DIR/app.log" ]; then
        tail -f "$LOG_DIR/app.log"
    elif [ -f "$LOG_DIR/app.out" ]; then
        tail -f "$LOG_DIR/app.out"
    else
        echo -e "${YELLOW}日志文件不存在${NC}"
    fi
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo -e "${RED}错误: 未知选项 '$1'${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
