package main

import (
	"fmt"
	"log"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/model"
	"solve_api/internal/utils"
)

func main() {
	fmt.Println("=== 创建新管理员账号 ===")
	
	// 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 初始化数据库
	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		log.Fatalf("❌ 数据库初始化失败: %v", err)
	}

	db := database.GetDB()

	// 手动修复表结构
	fmt.Println("修复管理员表结构...")

	// 检查并删除username字段
	var count int64
	err = db.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'go_solve' AND table_name = 'admins' AND column_name = 'username'").Scan(&count).Error
	if err == nil && count > 0 {
		fmt.Println("删除username字段...")
		db.Exec("ALTER TABLE admins DROP INDEX IF EXISTS idx_admins_username")
		db.Exec("ALTER TABLE admins DROP COLUMN username")
	}

	// 自动迁移表结构
	if err := db.AutoMigrate(&model.Admin{}); err != nil {
		log.Fatalf("❌ 自动迁移失败: %v", err)
	}

	// 创建默认超级管理员
	phone := "15688515913"
	password := "admin888"
	
	// 检查是否已存在
	var existingAdmin model.Admin
	err = db.Where("phone = ?", phone).First(&existingAdmin).Error
	if err == nil {
		fmt.Printf("✅ 管理员账号已存在: %s\n", phone)
		return
	}

	// 加密密码
	hashedPassword, err := utils.HashPassword(password)
	if err != nil {
		log.Fatalf("❌ 密码加密失败: %v", err)
	}

	// 创建管理员
	admin := model.Admin{
		Phone:    phone,
		Password: hashedPassword,
		Role:     model.AdminRoleSuperAdmin,
	}

	if err := db.Create(&admin).Error; err != nil {
		log.Fatalf("❌ 创建管理员失败: %v", err)
	}

	fmt.Printf("✅ 超级管理员账号创建成功!\n")
	fmt.Printf("   手机号: %s\n", admin.Phone)
	fmt.Printf("   密码: %s\n", password)
	fmt.Printf("   角色: %s\n", admin.GetRoleName())
	fmt.Printf("   ID: %d\n", admin.ID)
}
