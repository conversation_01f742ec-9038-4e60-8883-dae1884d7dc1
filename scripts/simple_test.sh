#!/bin/bash

# 简单的数据库连接测试脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== 简单数据库连接测试 ===${NC}"
echo ""

# MySQL连接信息
MYSQL_HOST="***********"
MYSQL_PORT="3380"
MYSQL_USER="gmdns"
MYSQL_PASS="5e7fFn3HpPfuQ6Qx42Az"
MYSQL_DB="go_solve"

# Redis连接信息
REDIS_HOST="************"
REDIS_PORT="6379"
REDIS_PASS="y4HY8xm8dECYmDSeaX8GC"

echo -e "${YELLOW}1. 测试网络连接${NC}"

# 测试MySQL服务器连接
echo -n "   MySQL服务器 ($MYSQL_HOST:$MYSQL_PORT): "
if nc -zv $MYSQL_HOST $MYSQL_PORT 2>/dev/null; then
    echo -e "${GREEN}✅ 可连接${NC}"
else
    echo -e "${RED}❌ 无法连接${NC}"
fi

# 测试Redis服务器连接
echo -n "   Redis服务器 ($REDIS_HOST:$REDIS_PORT): "
if nc -zv $REDIS_HOST $REDIS_PORT 2>/dev/null; then
    echo -e "${GREEN}✅ 可连接${NC}"
else
    echo -e "${RED}❌ 无法连接${NC}"
fi

echo ""
echo -e "${YELLOW}2. 测试MySQL数据库连接${NC}"

# 检查是否安装了mysql客户端
if command -v mysql &> /dev/null; then
    echo "   正在测试MySQL连接..."
    
    # 测试MySQL连接和查询
    MYSQL_RESULT=$(mysql -h$MYSQL_HOST -P$MYSQL_PORT -u$MYSQL_USER -p$MYSQL_PASS -D$MYSQL_DB -e "SELECT VERSION();" 2>&1)
    
    if [ $? -eq 0 ]; then
        echo -e "   ${GREEN}✅ MySQL连接成功${NC}"
        echo "   数据库版本: $(echo "$MYSQL_RESULT" | tail -n 1)"
        
        # 检查表
        echo "   正在检查数据库表..."
        TABLES=$(mysql -h$MYSQL_HOST -P$MYSQL_PORT -u$MYSQL_USER -p$MYSQL_PASS -D$MYSQL_DB -e "SHOW TABLES;" 2>/dev/null | tail -n +2)
        
        if [ -z "$TABLES" ]; then
            echo -e "   ${YELLOW}⚠️  数据库中没有表，需要运行迁移${NC}"
        else
            echo -e "   ${GREEN}✅ 找到以下表:${NC}"
            echo "$TABLES" | while read table; do
                echo "      - $table"
            done
        fi
    else
        echo -e "   ${RED}❌ MySQL连接失败${NC}"
        echo "   错误信息: $MYSQL_RESULT"
    fi
else
    echo -e "   ${YELLOW}⚠️  未安装mysql客户端，跳过MySQL连接测试${NC}"
    echo "   可以安装: brew install mysql-client (macOS) 或 apt-get install mysql-client (Ubuntu)"
fi

echo ""
echo -e "${YELLOW}3. 测试Redis连接${NC}"

# 检查是否安装了redis客户端
if command -v redis-cli &> /dev/null; then
    echo "   正在测试Redis连接..."
    
    # 测试Redis连接
    if [ -n "$REDIS_PASS" ]; then
        REDIS_RESULT=$(redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASS ping 2>/dev/null)
    else
        REDIS_RESULT=$(redis-cli -h $REDIS_HOST -p $REDIS_PORT ping 2>/dev/null)
    fi
    
    if [ "$REDIS_RESULT" = "PONG" ]; then
        echo -e "   ${GREEN}✅ Redis连接成功${NC}"
        
        # 获取Redis信息
        if [ -n "$REDIS_PASS" ]; then
            REDIS_VERSION=$(redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASS info server 2>/dev/null | grep "redis_version" | cut -d: -f2 | tr -d '\r')
        else
            REDIS_VERSION=$(redis-cli -h $REDIS_HOST -p $REDIS_PORT info server 2>/dev/null | grep "redis_version" | cut -d: -f2 | tr -d '\r')
        fi
        
        if [ -n "$REDIS_VERSION" ]; then
            echo "   Redis版本: $REDIS_VERSION"
        fi
    else
        echo -e "   ${RED}❌ Redis连接失败${NC}"
        echo "   响应: $REDIS_RESULT"
    fi
else
    echo -e "   ${YELLOW}⚠️  未安装redis-cli客户端，跳过Redis连接测试${NC}"
    echo "   可以安装: brew install redis (macOS) 或 apt-get install redis-tools (Ubuntu)"
fi

echo ""
echo -e "${BLUE}=== 测试总结 ===${NC}"
echo ""
echo -e "${YELLOW}配置信息:${NC}"
echo "  MySQL: $MYSQL_HOST:$MYSQL_PORT"
echo "  数据库: $MYSQL_DB"
echo "  用户: $MYSQL_USER"
echo "  Redis: $REDIS_HOST:$REDIS_PORT"
echo ""
echo -e "${YELLOW}下一步:${NC}"
echo "  1. 如果连接成功但没有表，运行: ./dev.sh start"
echo "  2. 如果连接失败，检查网络和防火墙设置"
echo "  3. 运行完整测试: ./dev.sh db (需要先安装Go依赖)"
echo ""
