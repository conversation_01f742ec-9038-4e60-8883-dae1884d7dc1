#!/bin/bash

# 数据库连接测试脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== 数据库连接测试 ===${NC}"
echo ""

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo -e "${RED}错误: Go未安装或不在PATH中${NC}"
    exit 1
fi

# 检查配置文件
if [ ! -f "config/config.yaml" ]; then
    echo -e "${RED}错误: 配置文件 config/config.yaml 不存在${NC}"
    exit 1
fi

echo -e "${YELLOW}正在测试数据库连接...${NC}"
echo ""

# 运行连接测试
cd "$(dirname "$0")/.."
go run scripts/test_connection.go

echo ""
echo -e "${BLUE}=== 测试完成 ===${NC}"
echo ""
echo -e "${YELLOW}如果连接失败，请检查:${NC}"
echo "  1. 网络连接是否正常"
echo "  2. MySQL服务器地址和端口是否正确 (***********:3380)"
echo "  3. Redis服务器地址和端口是否正确 (************:6379)"
echo "  4. 用户名和密码是否正确"
echo "  5. 防火墙是否允许连接"
echo ""
echo -e "${BLUE}配置文件位置: config/config.yaml${NC}"
