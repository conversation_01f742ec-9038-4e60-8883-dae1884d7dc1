-- 拍照搜题API数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS solve_api CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE solve_api;

-- 创建用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `phone` varchar(20) NOT NULL,
  `password` varchar(100) NOT NULL,
  `balance` decimal(10,2) DEFAULT '0.00',
  `status` int DEFAULT '1' COMMENT '1:正常 2:冻结',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_users_phone` (`phone`),
  KEY `idx_users_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS `system_configs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(50) NOT NULL,
  `value` text,
  `description` varchar(255) DEFAULT NULL,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_system_configs_key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认系统配置
INSERT INTO `system_configs` (`key`, `value`, `description`, `created_at`, `updated_at`) VALUES
('invite_code', 'SOLVE2024', '系统邀请码', NOW(), NOW()),
('rate_limit', '10', 'API限流配置（次/秒）', NOW(), NOW()),
('cache_ttl', '604800', '缓存TTL配置（秒）', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
  `updated_at` = NOW();

-- 创建应用表（为后续开发准备）
CREATE TABLE IF NOT EXISTS `applications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `name` varchar(50) NOT NULL,
  `type` int NOT NULL COMMENT '业务类型 1:拍照搜题',
  `app_key` varchar(32) NOT NULL,
  `secret_key` varchar(64) NOT NULL,
  `status` int DEFAULT '1' COMMENT '1:正常 2:冻结',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_applications_app_key` (`app_key`),
  KEY `idx_applications_user_id` (`user_id`),
  KEY `idx_applications_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建题目表（为后续开发准备）
CREATE TABLE IF NOT EXISTS `questions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `hash` varchar(64) NOT NULL COMMENT '题目哈希值',
  `content` text COMMENT '题目内容',
  `analysis` text COMMENT '题目解析',
  `answer` text COMMENT '题目答案',
  `subject` varchar(20) DEFAULT NULL COMMENT '学科',
  `grade` varchar(20) DEFAULT NULL COMMENT '年级',
  `difficulty` int DEFAULT NULL COMMENT '难度 1-5',
  `source_model` varchar(50) DEFAULT NULL COMMENT '来源模型',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_questions_hash` (`hash`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建模型配置表（为后续开发准备）
CREATE TABLE IF NOT EXISTS `model_configs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `api_url` varchar(255) DEFAULT NULL,
  `api_key` varchar(100) DEFAULT NULL,
  `params` text COMMENT 'JSON格式的参数配置',
  `status` int DEFAULT '1' COMMENT '1:启用 2:禁用',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_model_configs_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建价格配置表（为后续开发准备）
CREATE TABLE IF NOT EXISTS `price_configs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `service_type` int NOT NULL COMMENT '服务类型 1:拍照搜题',
  `user_id` bigint unsigned DEFAULT '0' COMMENT '0表示系统默认价格',
  `price` decimal(10,4) NOT NULL COMMENT '单次调用价格',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_price_configs_service_type` (`service_type`),
  KEY `idx_price_configs_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建余额变动日志表（为后续开发准备）
CREATE TABLE IF NOT EXISTS `balance_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `amount` decimal(10,4) NOT NULL COMMENT '变动金额',
  `balance` decimal(10,2) NOT NULL COMMENT '变动后余额',
  `type` int NOT NULL COMMENT '类型 1:充值 2:消费 3:退款',
  `description` varchar(255) DEFAULT NULL,
  `related_id` bigint unsigned DEFAULT NULL COMMENT '关联ID',
  `created_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_balance_logs_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建请求日志表（为后续开发准备）
CREATE TABLE IF NOT EXISTS `request_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `app_id` bigint unsigned DEFAULT NULL,
  `path` varchar(100) DEFAULT NULL,
  `method` varchar(10) DEFAULT NULL,
  `params` text,
  `response` text,
  `status` int DEFAULT NULL COMMENT 'HTTP状态码',
  `cost` int DEFAULT NULL COMMENT '耗时(ms)',
  `ip` varchar(50) DEFAULT NULL,
  `created_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_request_logs_user_id` (`user_id`),
  KEY `idx_request_logs_app_id` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建模型调用日志表（为后续开发准备）
CREATE TABLE IF NOT EXISTS `model_call_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `app_id` bigint unsigned DEFAULT NULL,
  `model_name` varchar(50) DEFAULT NULL,
  `cache_hit` tinyint(1) DEFAULT NULL COMMENT '是否命中缓存',
  `cost` int DEFAULT NULL COMMENT '耗时(ms)',
  `success` tinyint(1) DEFAULT NULL COMMENT '是否成功',
  `created_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_model_call_logs_user_id` (`user_id`),
  KEY `idx_model_call_logs_app_id` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建管理员表（为后续开发准备）
CREATE TABLE IF NOT EXISTS `admins` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(100) NOT NULL,
  `role` int NOT NULL COMMENT '角色 1:超级管理员 2:普通管理员',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_admins_username` (`username`),
  KEY `idx_admins_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认管理员账户（密码: admin123）
INSERT INTO `admins` (`username`, `password`, `role`, `created_at`, `updated_at`) VALUES
('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
  `updated_at` = NOW();

-- 插入默认价格配置
INSERT INTO `price_configs` (`service_type`, `user_id`, `price`, `created_at`, `updated_at`) VALUES
(1, 0, 0.01, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
  `updated_at` = NOW();

-- 插入默认模型配置
INSERT INTO `model_configs` (`name`, `api_url`, `api_key`, `params`, `status`, `created_at`, `updated_at`) VALUES
('qwen-vl-plus', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation', '', '{"model":"qwen-vl-plus","temperature":0.1}', 1, NOW(), NOW()),
('deepseek-chat', 'https://api.deepseek.com/v1/chat/completions', '', '{"model":"deepseek-chat","temperature":0.1}', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
  `updated_at` = NOW();

COMMIT;
