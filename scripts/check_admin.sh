#!/bin/bash

# 管理员账号检查脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== 管理员账号检查工具 ===${NC}"
echo ""

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo -e "${RED}错误: Go未安装或不在PATH中${NC}"
    exit 1
fi

# 检查配置文件
if [ ! -f "config/config.yaml" ]; then
    echo -e "${RED}错误: 配置文件 config/config.yaml 不存在${NC}"
    exit 1
fi

echo -e "${YELLOW}正在检查管理员账号...${NC}"
echo ""

# 运行检查脚本
cd "$(dirname "$0")/.."
go run scripts/check_admin.go

echo ""
echo -e "${BLUE}=== 检查完成 ===${NC}"
echo ""
echo -e "${YELLOW}默认管理员账号信息:${NC}"
echo "  用户名: admin"
echo "  密码: admin123"
echo "  角色: 超级管理员"
echo ""
echo -e "${YELLOW}安全提醒:${NC}"
echo "  ⚠️  请在生产环境中立即修改默认密码"
echo "  🔐 建议使用强密码（包含大小写字母、数字、特殊字符）"
echo "  👥 为不同管理员创建独立账号"
echo ""
echo -e "${BLUE}相关文档: ADMIN_ACCOUNT.md${NC}"
