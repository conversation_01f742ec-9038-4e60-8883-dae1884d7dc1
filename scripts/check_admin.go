package main

import (
	"fmt"
	"log"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/model"
	"solve_api/internal/utils"
)

// 检查管理员账号的工具脚本
func main() {
	fmt.Println("=== 管理员账号检查工具 ===")

	// 1. 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 2. 初始化数据库
	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		log.Fatalf("Failed to initialize MySQL: %v", err)
	}
	defer database.CloseMySQL()

	// 3. 自动迁移（确保表存在）
	db := database.GetDB()
	if err := db.AutoMigrate(&model.Admin{}); err != nil {
		log.Fatalf("Failed to migrate admin table: %v", err)
	}

	// 4. 检查当前管理员账号
	checkCurrentAdmin()

	// 5. 列出所有管理员
	listAllAdmins()

	// 6. 验证密码
	verifyCurrentPassword()
}

// 检查当前管理员账号
func checkCurrentAdmin() {
	fmt.Println("\n--- 检查当前管理员账号 ---")

	db := database.GetDB()
	var admin model.Admin

	// 先查找新的管理员账号
	err := db.Where("username = ?", "15688515913").First(&admin).Error
	if err == nil {
		fmt.Printf("✅ 找到当前管理员账号:\n")
		fmt.Printf("   ID: %d\n", admin.ID)
		fmt.Printf("   用户名: %s\n", admin.Username)
		fmt.Printf("   角色: %d (%s)\n", admin.Role, admin.GetRoleName())
		fmt.Printf("   创建时间: %s\n", admin.CreatedAt.Format("2006-01-02 15:04:05"))
		fmt.Printf("   更新时间: %s\n", admin.UpdatedAt.Format("2006-01-02 15:04:05"))
		return
	}

	// 如果没找到，再查找默认的admin账号
	err = db.Where("username = ?", "admin").First(&admin).Error
	if err != nil {
		fmt.Printf("❌ 管理员账号不存在: %v\n", err)
		fmt.Println("💡 请运行数据库初始化脚本创建管理员账号")
		return
	}

	fmt.Printf("✅ 找到默认管理员账号:\n")
	fmt.Printf("   ID: %d\n", admin.ID)
	fmt.Printf("   用户名: %s\n", admin.Username)
	fmt.Printf("   角色: %d (%s)\n", admin.Role, admin.GetRoleName())
	fmt.Printf("   创建时间: %s\n", admin.CreatedAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("   更新时间: %s\n", admin.UpdatedAt.Format("2006-01-02 15:04:05"))
}

// 列出所有管理员
func listAllAdmins() {
	fmt.Println("\n--- 所有管理员账号 ---")

	db := database.GetDB()
	var admins []model.Admin

	err := db.Find(&admins).Error
	if err != nil {
		fmt.Printf("❌ 查询管理员失败: %v\n", err)
		return
	}

	if len(admins) == 0 {
		fmt.Println("📝 没有找到任何管理员账号")
		return
	}

	fmt.Printf("📋 共找到 %d 个管理员账号:\n", len(admins))
	for i, admin := range admins {
		fmt.Printf("   %d. %s (角色: %s, ID: %d)\n",
			i+1, admin.Username, admin.GetRoleName(), admin.ID)
	}
}

// 验证当前密码
func verifyCurrentPassword() {
	fmt.Println("\n--- 验证当前密码 ---")

	db := database.GetDB()
	var admin model.Admin

	// 先查找新的管理员账号
	err := db.Where("username = ?", "15688515913").First(&admin).Error
	if err == nil {
		// 验证当前密码 "admin888"
		currentPassword := "admin888"
		if utils.CheckPassword(currentPassword, admin.Password) {
			fmt.Printf("✅ 当前密码验证成功\n")
			fmt.Printf("   用户名: %s\n", admin.Username)
			fmt.Printf("   密码: %s\n", currentPassword)
			fmt.Printf("✅ 密码已更新为安全密码\n")
		} else {
			fmt.Printf("❌ 当前密码验证失败\n")
			fmt.Printf("💡 密码可能已被修改\n")
		}
		return
	}

	// 如果没找到，再查找默认的admin账号
	err = db.Where("username = ?", "admin").First(&admin).Error
	if err != nil {
		fmt.Printf("❌ 找不到管理员账号\n")
		return
	}

	// 验证默认密码 "admin123"
	defaultPassword := "admin123"
	if utils.CheckPassword(defaultPassword, admin.Password) {
		fmt.Printf("✅ 默认密码验证成功\n")
		fmt.Printf("   用户名: admin\n")
		fmt.Printf("   密码: %s\n", defaultPassword)
		fmt.Printf("⚠️  请在生产环境中修改默认密码！\n")
	} else {
		fmt.Printf("❌ 默认密码验证失败\n")
		fmt.Printf("💡 密码可能已被修改，这是安全的做法\n")
	}
}
