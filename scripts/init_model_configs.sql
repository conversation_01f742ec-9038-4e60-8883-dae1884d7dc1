-- 初始化模型配置
-- 执行时间: 2024-01-01

-- 插入或更新 Qwen-VL-Plus 模型配置
INSERT INTO model_configs (name, api_url, api_key, params, status, created_at, updated_at)
VALUES (
    'qwen-vl-plus',
    'https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation',
    '',  -- API Key 需要手动配置
    JSON_OBJECT(
        'temperature', 0.3,
        'max_tokens', 1500,
        'top_p', 0.8,
        'response_format', JSON_OBJECT('type', 'json_object'),
        'detail', 'high'
    ),
    1,  -- 启用状态
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    api_url = VALUES(api_url),
    params = VALUES(params),
    updated_at = NOW();

-- 插入或更新 Deepseek-Chat 模型配置
INSERT INTO model_configs (name, api_url, api_key, params, status, created_at, updated_at)
VALUES (
    'deepseek-chat',
    'https://api.deepseek.com/v1/chat/completions',
    '***********************************',  -- 现有的 API Key
    JSON_OBJECT(
        'temperature', 0.3,
        'max_tokens', 2500,
        'top_p', 0.8,
        'response_format', JSON_OBJECT('type', 'json_object')
    ),
    1,  -- 启用状态
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    api_url = VALUES(api_url),
    api_key = VALUES(api_key),
    params = VALUES(params),
    updated_at = NOW();

-- 查看配置结果
SELECT 
    name,
    api_url,
    CASE WHEN api_key = '' THEN '未配置' ELSE '已配置' END as api_key_status,
    params,
    CASE WHEN status = 1 THEN '启用' ELSE '禁用' END as status_name,
    created_at,
    updated_at
FROM model_configs
WHERE name IN ('qwen-vl-plus', 'deepseek-chat')
ORDER BY name;
