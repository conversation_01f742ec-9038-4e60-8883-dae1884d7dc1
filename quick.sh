#!/bin/bash

# 快速测试脚本 - 一键启动和测试

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}🚀 快速启动测试...${NC}"

# 停止可能运行的服务
./dev.sh stop > /dev/null 2>&1

# 启动服务
echo -e "${YELLOW}启动服务...${NC}"
./dev.sh start

# 等待服务完全启动
echo -e "${YELLOW}等待服务启动...${NC}"
sleep 3

# 检查健康状态
echo -e "${YELLOW}检查服务状态...${NC}"
./dev.sh health

echo ""
echo -e "${GREEN}✅ 服务已启动，可以开始测试！${NC}"
echo ""
echo -e "${BLUE}常用命令:${NC}"
echo "  ./dev.sh test    - 运行API测试"
echo "  ./dev.sh logs    - 查看实时日志"
echo "  ./dev.sh status  - 查看服务状态"
echo "  ./dev.sh stop    - 停止服务"
echo ""
echo -e "${BLUE}API地址: http://localhost:8080${NC}"
echo -e "${BLUE}健康检查: http://localhost:8080/health${NC}"
