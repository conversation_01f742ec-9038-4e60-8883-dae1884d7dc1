#!/bin/bash

# 测试重构后的拍照搜题API
# 验证新的数据结构和业务流程

BASE_URL="http://localhost:8080"
USER_ID=1

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    local headers=$5

    echo -e "${YELLOW}测试: $description${NC}"
    echo "请求: $method $url"
    
    if [ -n "$data" ]; then
        echo "数据: $data"
    fi
    
    if [ -n "$headers" ]; then
        response=$(curl -s -X $method "$url" -H "Content-Type: application/json" $headers -d "$data")
    else
        response=$(curl -s -X $method "$url" -H "Content-Type: application/json" -d "$data")
    fi
    
    echo "响应: $response"
    
    # 检查响应是否包含成功标识
    if echo "$response" | grep -q '"code":200'; then
        echo -e "${GREEN}✓ 测试通过${NC}"
    else
        echo -e "${RED}✗ 测试失败${NC}"
    fi
    
    echo "----------------------------------------"
    echo
}

echo -e "${GREEN}=== 重构后的拍照搜题API测试 ===${NC}"
echo

# 1. 健康检查
echo -e "${YELLOW}1. 健康检查${NC}"
test_api "GET" "$BASE_URL/health" "" "服务健康检查"

# 2. 用户登录（获取用户信息）
echo -e "${YELLOW}2. 用户登录测试${NC}"
test_api "POST" "$BASE_URL/api/v1/user/login" \
    '{"phone":"15653259315","password":"password123"}' \
    "用户登录"

# 3. 创建应用（获取API密钥）
echo -e "${YELLOW}3. 创建应用测试${NC}"
test_api "POST" "$BASE_URL/api/v1/user/$USER_ID/app" \
    '{"name":"重构测试应用","type":1}' \
    "创建测试应用"

# 4. 获取应用列表
echo -e "${YELLOW}4. 获取应用列表${NC}"
test_api "GET" "$BASE_URL/api/v1/user/$USER_ID/app" \
    "" \
    "获取用户应用列表"

# 5. 获取应用详情（包含密钥）
echo -e "${YELLOW}5. 获取应用详情${NC}"
test_api "GET" "$BASE_URL/api/v1/user/$USER_ID/app/1" \
    "" \
    "获取应用详情和密钥"

echo -e "${GREEN}=== 测试完成 ===${NC}"
echo
echo -e "${YELLOW}注意事项:${NC}"
echo "1. 拍照搜题API需要有效的应用密钥"
echo "2. 需要配置Qwen-VL和Deepseek的API密钥"
echo "3. 新的数据结构支持更丰富的题目类型"
echo "4. 缓存键生成逻辑已优化"
echo
echo -e "${YELLOW}下一步:${NC}"
echo "1. 配置AI模型的API密钥"
echo "2. 执行数据库迁移脚本"
echo "3. 测试实际的拍照搜题功能"
