# 拍照搜题API独立接口文档

## 📸 接口概述

拍照搜题API是一个独立的图像识别和题目解析服务，支持上传图片进行题目识别、解析和答案生成。

## 🔗 接口信息

- **接口地址**: `POST /api/v1/api/search`
- **完整URL**: `http://localhost:8080/api/v1/api/search`
- **请求方法**: `POST`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 🔐 认证方式

### 方式1: 请求头认证（推荐）

**请求格式**:
```http
POST /api/v1/api/search HTTP/1.1
Host: localhost:8080
Content-Type: application/json
X-App-Key: your_app_key
X-Secret-Key: your_secret_key

{
  "image_url": "https://example.com/question.jpg"
}
```

**优势**: 符合HTTP标准，安全性高，不会被日志记录

### 方式2: 请求体认证（简化集成）

**请求格式**:
```http
POST /api/v1/api/search HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "app_key": "your_app_key",
  "secret_key": "your_secret_key",
  "image_url": "https://example.com/question.jpg"
}
```

**优势**: 集成简单，适合快速开发，移动端友好

### 方式3: 嵌套认证（结构化）

**请求格式**:
```http
POST /api/v1/api/search HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "auth": {
    "app_key": "your_app_key",
    "secret_key": "your_secret_key"
  },
  "image_url": "https://example.com/question.jpg"
}
```

**优势**: 结构清晰，认证信息与业务数据分离

## 📝 请求参数

### 必填参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `image_url` | string | 图片URL地址，必须可公开访问 | `"https://example.com/question.jpg"` |

### 认证参数（根据认证方式选择）

| 参数名 | 类型 | 位置 | 说明 |
|--------|------|------|------|
| `app_key` | string | 请求头/请求体 | 应用密钥 |
| `secret_key` | string | 请求头/请求体 | 应用秘钥 |

### 图片要求

- **支持格式**: jpg、png、gif、webp、bmp
- **文件大小**: 建议1-10MB
- **图片质量**: 清晰可读，包含完整题目
- **访问要求**: 必须可公开访问，无需认证

## 📊 响应格式

### 成功响应 (HTTP 200)

```json
{
  "code": 200,
  "message": "搜题成功",
  "data": {
    "id": 1,
    "content": "下列哪个是质数？",
    "question_type": "单选题",
    "question_text": "下列哪个是质数？",
    "options": {
      "A": "4",
      "B": "6",
      "C": "7",
      "D": "8"
    },
    "analysis": "质数是只能被1和自身整除的大于1的自然数。选项中：A.4=2×2，B.6=2×3，C.7只能被1和7整除，D.8=2×4。因此答案是C。",
    "answer": "C",
    "subject": "数学",
    "grade": "小学",
    "difficulty": 1,
    "source_model": "qwen-vl-plus,deepseek-chat",
    "cache_hit": false,
    "process_time": 1500
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `code` | number | 响应状态码，200表示成功 |
| `message` | string | 响应消息 |
| `data` | object | 搜题结果数据 |

#### data字段详细说明

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `id` | number | 题目唯一标识 | `1` |
| `content` | string | 题目内容（兼容字段） | `"下列哪个是质数？"` |
| `question_type` | string | 题目类型 | `"单选题"、"多选题"、"填空题"、"解答题"、"判断题"` |
| `question_text` | string | 结构化题目内容 | `"下列哪个是质数？"` |
| `options` | object | 选项内容（仅选择题有） | `{"A":"4","B":"6","C":"7","D":"8"}` |
| `analysis` | string | 详细解题思路和步骤 | `"质数是只能被1和自身整除..."` |
| `answer` | string | 最终答案 | `"C"` |
| `subject` | string | 学科分类 | `"数学"、"语文"、"英语"、"物理"等` |
| `grade` | string | 年级分类 | `"小学"、"初中"、"高中"、"大学"` |
| `difficulty` | number | 难度等级（1-5） | `1`（最简单）到`5`（最困难） |
| `source_model` | string | 使用的AI模型 | `"qwen-vl-plus,deepseek-chat"` |
| `cache_hit` | boolean | 是否命中缓存 | `true`/`false` |
| `process_time` | number | 处理时间（毫秒） | `1500` |

## ❌ 错误响应

### 错误响应格式

```json
{
  "code": 错误码,
  "message": "错误描述",
  "data": null
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 错误描述 | 解决方案 |
|--------|------------|----------|----------|
| 400 | 400 | 请求参数错误 | 检查图片URL格式和参数完整性 |
| 401 | 401 | API密钥无效 | 检查app_key和secret_key是否正确 |
| 402 | 402 | 余额不足 | 用户需要充值账户余额 |
| 403 | 403 | 应用被冻结 | 联系管理员解冻应用 |
| 404 | 404 | 图片无法访问 | 检查图片URL是否可访问 |
| 429 | 429 | 请求频率过高 | 降低请求频率，实现限流 |
| 500 | 500 | 服务器内部错误 | 稍后重试或联系技术支持 |

### 错误响应示例

#### 401 认证失败
```json
{
  "code": 401,
  "message": "API密钥无效",
  "data": null
}
```

#### 402 余额不足
```json
{
  "code": 402,
  "message": "余额不足，请先充值",
  "data": null
}
```

#### 400 参数错误
```json
{
  "code": 400,
  "message": "图片URL不能为空",
  "data": null
}
```

## 🚀 调用示例

### JavaScript/Fetch

#### 请求头认证
```javascript
const searchQuestionWithHeaders = async (imageUrl, appKey, secretKey) => {
  try {
    const response = await fetch('http://localhost:8080/api/v1/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-App-Key': appKey,
        'X-Secret-Key': secretKey
      },
      body: JSON.stringify({
        image_url: imageUrl
      })
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('搜题成功:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('搜题失败:', error);
    throw error;
  }
};
```

#### 请求体认证
```javascript
const searchQuestionWithBody = async (imageUrl, appKey, secretKey) => {
  try {
    const response = await fetch('http://localhost:8080/api/v1/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        app_key: appKey,
        secret_key: secretKey,
        image_url: imageUrl
      })
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    throw error;
  }
};
```

#### 使用示例
```javascript
// 调用搜题API
searchQuestionWithBody(
  'https://example.com/question.jpg',
  'your_app_key',
  'your_secret_key'
).then(data => {
  console.log('题目类型:', data.question_type);
  console.log('题目内容:', data.question_text);
  console.log('选项:', data.options);
  console.log('答案:', data.answer);
  console.log('解析:', data.analysis);
}).catch(error => {
  console.error('搜题失败:', error.message);
});
```

### Python requests

```python
import requests
import json

def search_question(image_url, app_key, secret_key, auth_method='body'):
    """
    拍照搜题API调用
    
    Args:
        image_url (str): 图片URL
        app_key (str): 应用密钥
        secret_key (str): 应用秘钥
        auth_method (str): 认证方式 'header' 或 'body'
    
    Returns:
        dict: 搜题结果
    """
    url = "http://localhost:8080/api/v1/api/search"
    
    if auth_method == 'header':
        headers = {
            'Content-Type': 'application/json',
            'X-App-Key': app_key,
            'X-Secret-Key': secret_key
        }
        data = {
            'image_url': image_url
        }
    else:
        headers = {
            'Content-Type': 'application/json'
        }
        data = {
            'app_key': app_key,
            'secret_key': secret_key,
            'image_url': image_url
        }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        result = response.json()
        
        if result['code'] == 200:
            return result['data']
        else:
            raise Exception(result['message'])
            
    except requests.exceptions.RequestException as e:
        raise Exception(f"请求失败: {e}")

# 使用示例
try:
    result = search_question(
        "https://example.com/question.jpg",
        "your_app_key",
        "your_secret_key"
    )
    
    print(f"题目类型: {result['question_type']}")
    print(f"题目内容: {result['question_text']}")
    print(f"答案: {result['answer']}")
    print(f"解析: {result['analysis']}")
    
except Exception as e:
    print(f"搜题失败: {e}")
```

### curl 命令

#### 请求头认证
```bash
curl -X POST http://localhost:8080/api/v1/api/search \
  -H "Content-Type: application/json" \
  -H "X-App-Key: your_app_key" \
  -H "X-Secret-Key: your_secret_key" \
  -d '{
    "image_url": "https://example.com/question.jpg"
  }'
```

#### 请求体认证
```bash
curl -X POST http://localhost:8080/api/v1/api/search \
  -H "Content-Type: application/json" \
  -d '{
    "app_key": "your_app_key",
    "secret_key": "your_secret_key",
    "image_url": "https://example.com/question.jpg"
  }'
```

## 💰 计费说明

### 计费规则
- **计费方式**: 按次计费
- **标准价格**: 0.1元/次
- **扣费时机**: 成功返回结果后扣费
- **缓存优化**: 相同题目命中缓存不重复扣费

### 余额管理
- 余额不足时返回402错误码
- 需要通过充值接口增加余额
- 可通过余额查询接口检查当前余额

## 🔧 性能优化

### 缓存机制
- 相同题目内容会命中缓存
- 缓存命中时`cache_hit`字段为`true`
- 缓存命中响应时间显著降低（通常<100ms）

### 请求优化建议
1. **超时设置**: 建议设置30秒超时
2. **重试机制**: 实现指数退避重试
3. **限流控制**: 避免频繁请求，建议10次/秒以内
4. **图片优化**: 压缩图片大小，提高传输效率

### 错误处理最佳实践
```javascript
const searchWithRetry = async (imageUrl, appKey, secretKey, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await searchQuestion(imageUrl, appKey, secretKey);
      return result;
    } catch (error) {
      if (error.code === 429 && i < maxRetries - 1) {
        // 频率限制，等待后重试
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        continue;
      }
      throw error;
    }
  }
};
```

## 📞 技术支持

### 常见问题
1. **Q**: 如何获取API密钥？
   **A**: 需要先注册用户，创建应用后获取app_key和secret_key

2. **Q**: 支持哪些图片格式？
   **A**: 支持jpg、png、gif、webp、bmp等常见格式

3. **Q**: 图片大小有限制吗？
   **A**: 建议1-10MB，过大可能影响处理速度

4. **Q**: 如何提高识别准确率？
   **A**: 使用清晰、完整、光线良好的题目图片

### 联系方式
- 📧 技术支持: <EMAIL>
- 📱 客服电话: 400-123-4567
- 💬 在线客服: 工作日 9:00-18:00

---

**注意**: 本API为独立服务，可单独集成使用。生产环境请使用HTTPS协议确保数据传输安全。
