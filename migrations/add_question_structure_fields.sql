-- 添加新的题目结构化字段
-- 执行时间: 2024-01-01

-- 添加新字段到 questions 表
ALTER TABLE questions 
ADD COLUMN question_type VARCHAR(20) COMMENT '题目类型' AFTER content,
ADD COLUMN question_text TEXT COMMENT '结构化题目内容' AFTER question_type,
ADD COLUMN options TEXT COMMENT '选项JSON格式' AFTER question_text,
ADD COLUMN raw_content TEXT COMMENT '原始识别内容' AFTER source_model;

-- 为新字段添加索引
CREATE INDEX idx_questions_question_type ON questions(question_type);
CREATE INDEX idx_questions_subject_grade ON questions(subject, grade);

-- 更新现有数据：将 content 复制到 question_text 和 raw_content
UPDATE questions 
SET question_text = content, 
    raw_content = content 
WHERE question_text IS NULL OR question_text = '';

-- 添加注释
ALTER TABLE questions MODIFY COLUMN content TEXT NOT NULL COMMENT '题目内容（兼容字段）';
ALTER TABLE questions MODIFY COLUMN question_type VARCHAR(20) COMMENT '题目类型：单选题、多选题、填空题、解答题等';
ALTER TABLE questions MODIFY COLUMN question_text TEXT COMMENT '结构化题目内容（不含序号和类型标识）';
ALTER TABLE questions MODIFY COLUMN options TEXT COMMENT '选项JSON格式，如：{"A":"选项A","B":"选项B"}';
ALTER TABLE questions MODIFY COLUMN raw_content TEXT COMMENT '原始识别内容';
