package main

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	_ "github.com/go-sql-driver/mysql"
)

func main() {
	fmt.Println("=== 数据库连接调试工具 ===")

	// MySQL连接信息
	mysqlHost := "***********"
	mysqlPort := "3380"
	mysqlUser := "gmdns"
	mysqlPass := "5e7fFn3HpPfuQ6Qx42Az"
	mysqlDB := "go_solve"

	// Redis连接信息
	redisHost := "************"
	redisPort := "6379"
	redisPass := "4HY8xm8dE"

	// 测试MySQL连接
	testMySQL(mysqlHost, mysqlPort, mysqlUser, mysqlPass, mysqlDB)

	// 测试Redis连接
	testRedis(redisHost, redisPort, redisPass)

	// 测试无密码连接
	fmt.Println("\n--- 测试Redis无密码连接 ---")
	testRedisAuth(fmt.Sprintf("%s:%s", redisHost, redisPort), "", "")
}

func testMySQL(host, port, user, pass, dbname string) {
	fmt.Println("\n--- 测试MySQL连接 ---")

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		user, pass, host, port, dbname)

	fmt.Printf("连接信息: %s:***@tcp(%s:%s)/%s\n", user, host, port, dbname)

	// 尝试连接
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		fmt.Printf("❌ MySQL连接失败: %v\n", err)
		return
	}
	defer db.Close()

	// 设置连接参数
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(time.Hour)

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err = db.PingContext(ctx)
	if err != nil {
		fmt.Printf("❌ MySQL Ping失败: %v\n", err)
		return
	}

	fmt.Printf("✅ MySQL连接成功\n")

	// 查询数据库版本
	var version string
	err = db.QueryRowContext(ctx, "SELECT VERSION()").Scan(&version)
	if err != nil {
		fmt.Printf("❌ 查询版本失败: %v\n", err)
	} else {
		fmt.Printf("   数据库版本: %s\n", version)
	}

	// 查询当前数据库
	var currentDB string
	err = db.QueryRowContext(ctx, "SELECT DATABASE()").Scan(&currentDB)
	if err != nil {
		fmt.Printf("❌ 查询当前数据库失败: %v\n", err)
	} else {
		fmt.Printf("   当前数据库: %s\n", currentDB)
	}

	// 查询表列表
	rows, err := db.QueryContext(ctx, "SHOW TABLES")
	if err != nil {
		fmt.Printf("❌ 查询表列表失败: %v\n", err)
	} else {
		defer rows.Close()
		var tables []string
		for rows.Next() {
			var table string
			if err := rows.Scan(&table); err == nil {
				tables = append(tables, table)
			}
		}
		fmt.Printf("   表数量: %d\n", len(tables))
		if len(tables) > 0 {
			fmt.Printf("   表列表: %v\n", tables)
		}
	}
}

func testRedis(host, port, password string) {
	fmt.Println("\n--- 测试Redis连接 ---")

	addr := fmt.Sprintf("%s:%s", host, port)
	fmt.Printf("连接信息: %s (密码: %s)\n", addr, maskPassword(password))

	// 测试不同的认证方式
	testRedisAuth(addr, "", password)           // 无用户名
	testRedisAuth(addr, "default", password)    // 默认用户名
	testRedisAuth(addr, "admin", password)      // admin用户名
}

func testRedisAuth(addr, username, password string) {
	authInfo := "无用户名"
	if username != "" {
		authInfo = fmt.Sprintf("用户名: %s", username)
	}
	fmt.Printf("\n尝试连接 (%s):\n", authInfo)

	// 创建Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr:     addr,
		Username: username,
		Password: password,
		DB:       0,
	})
	defer rdb.Close()

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	pong, err := rdb.Ping(ctx).Result()
	if err != nil {
		fmt.Printf("   ❌ 连接失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ 连接成功: %s\n", pong)

	// 获取Redis信息
	info, err := rdb.Info(ctx, "server").Result()
	if err != nil {
		fmt.Printf("   ❌ 获取信息失败: %v\n", err)
	} else {
		// 解析版本信息
		if version := extractRedisVersion(info); version != "" {
			fmt.Printf("   Redis版本: %s\n", version)
		}
	}

	// 测试读写
	testKey := fmt.Sprintf("test_%d", time.Now().Unix())
	err = rdb.Set(ctx, testKey, "test_value", time.Minute).Err()
	if err != nil {
		fmt.Printf("   ❌ 写入测试失败: %v\n", err)
	} else {
		val, err := rdb.Get(ctx, testKey).Result()
		if err != nil {
			fmt.Printf("   ❌ 读取测试失败: %v\n", err)
		} else {
			fmt.Printf("   ✅ 读写测试成功: %s\n", val)
			rdb.Del(ctx, testKey) // 清理
		}
	}
}

func maskPassword(password string) string {
	if len(password) <= 4 {
		return "***"
	}
	return password[:2] + "***" + password[len(password)-2:]
}

func extractRedisVersion(info string) string {
	lines := []string{}
	for _, line := range []string{info} {
		if len(line) > 0 {
			lines = append(lines, line)
		}
	}
	
	for _, line := range lines {
		if len(line) > 13 && line[:13] == "redis_version" {
			parts := []string{}
			for i, part := range []string{line} {
				if i > 0 {
					parts = append(parts, part)
				}
			}
			if len(parts) > 0 {
				return parts[0]
			}
		}
	}
	return ""
}
