# 拍照搜题API接口文档

## 📋 概述

本文档详细说明拍照搜题API的接入方式，为前端开发团队提供完整的集成指南。该API支持图片识别、题目解析、多种题目类型识别等功能。

## 🔗 基础信息

- **服务地址**: `http://localhost:8080`
- **API版本**: v1
- **内容类型**: `application/json`
- **字符编码**: UTF-8

## 🔐 认证方式

拍照搜题API支持**三种认证方式**，开发者可根据需要选择：

### 方式1: 请求头认证（推荐）

```http
X-App-Key: your_app_key
X-Secret-Key: your_secret_key
```

**优势**: 符合HTTP标准，安全性高，不会被日志记录

### 方式2: 请求体认证（简化集成）

```json
{
  "app_key": "your_app_key",
  "secret_key": "your_secret_key",
  "image_url": "https://example.com/image.jpg"
}
```

**优势**: 集成简单，适合快速开发

### 方式3: 嵌套认证（结构化）

```json
{
  "auth": {
    "app_key": "your_app_key",
    "secret_key": "your_secret_key"
  },
  "image_url": "https://example.com/image.jpg"
}
```

**优势**: 结构清晰，便于管理

### 获取应用密钥

1. 用户注册/登录
2. 创建应用获取 `app_key`
3. 查看应用详情获取 `secret_key`

## 📝 核心接口

### 拍照搜题

**接口地址**: `POST /api/v1/api/search`

### 请求示例

#### 方式1: 请求头认证
```http
POST /api/v1/api/search
Content-Type: application/json
X-App-Key: app_1234567890abcdef
X-Secret-Key: sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890

{
  "image_url": "https://example.com/question.jpg"
}
```

#### 方式2: 请求体认证
```http
POST /api/v1/api/search
Content-Type: application/json

{
  "app_key": "app_1234567890abcdef",
  "secret_key": "sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "image_url": "https://example.com/question.jpg"
}
```

#### 方式3: 嵌套认证
```http
POST /api/v1/api/search
Content-Type: application/json

{
  "auth": {
    "app_key": "app_1234567890abcdef",
    "secret_key": "sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
  },
  "image_url": "https://example.com/question.jpg"
}
```

**参数说明**:
- `image_url`: 图片URL地址，必填
  - 支持格式：jpg、png、gif、webp、bmp
  - 建议大小：不超过10MB
  - 要求：图片必须可公开访问
- `app_key`: 应用密钥（请求体认证时必填）
- `secret_key`: 应用秘钥（请求体认证时必填）

**成功响应**:
```json
{
  "code": 200,
  "message": "搜题成功",
  "data": {
    "id": 1,
    "content": "解这个方程：2x + 3 = 7",
    "question_type": "解答题",
    "question_text": "解这个方程：2x + 3 = 7",
    "options": {},
    "analysis": "解：2x + 3 = 7\n移项得：2x = 7 - 3\n化简得：2x = 4\n两边同时除以2：x = 2",
    "answer": "x = 2",
    "subject": "数学",
    "grade": "初中",
    "difficulty": 2,
    "source_model": "qwen-vl-plus,deepseek-chat",
    "cache_hit": false,
    "process_time": 1500
  }
}
```

**选择题响应示例**:
```json
{
  "code": 200,
  "message": "搜题成功",
  "data": {
    "id": 2,
    "content": "下列哪个是质数？",
    "question_type": "单选题",
    "question_text": "下列哪个是质数？",
    "options": {
      "A": "4",
      "B": "6", 
      "C": "7",
      "D": "8"
    },
    "analysis": "质数是只能被1和自身整除的大于1的自然数。选项中：A.4=2×2，B.6=2×3，C.7只能被1和7整除，D.8=2×4。因此答案是C。",
    "answer": "C",
    "subject": "数学",
    "grade": "小学",
    "difficulty": 1,
    "source_model": "qwen-vl-plus,deepseek-chat",
    "cache_hit": true,
    "process_time": 50
  }
}
```

**响应字段说明**:
- `id`: 题目唯一标识
- `content`: 题目内容（兼容字段）
- `question_type`: 题目类型（单选题、多选题、填空题、解答题、判断题等）
- `question_text`: 结构化题目内容（不含序号和类型标识）
- `options`: 选项内容（仅选择题有）
- `analysis`: 详细解题思路和步骤
- `answer`: 最终答案
- `subject`: 学科（数学、语文、英语、物理、化学等）
- `grade`: 年级（小学、初中、高中、大学等）
- `difficulty`: 难度等级（1-5，1最简单，5最困难）
- `source_model`: 使用的AI模型
- `cache_hit`: 是否命中缓存
- `process_time`: 处理时间（毫秒）

## ❌ 错误响应

### 400 - 请求参数错误
```json
{
  "code": 400,
  "message": "图片URL不能为空",
  "data": null
}
```

### 401 - 认证失败
```json
{
  "code": 401,
  "message": "API密钥无效",
  "data": null
}
```

### 402 - 余额不足
```json
{
  "code": 402,
  "message": "余额不足，请先充值",
  "data": null
}
```

### 403 - 权限不足
```json
{
  "code": 403,
  "message": "应用已被冻结",
  "data": null
}
```

### 429 - 请求频率过高
```json
{
  "code": 429,
  "message": "请求频率过高，请稍后重试",
  "data": null
}
```

### 500 - 服务器错误
```json
{
  "code": 500,
  "message": "图像识别失败",
  "data": null
}
```

## 💰 计费说明

- **计费方式**: 按次计费
- **价格**: 0.1元/次
- **扣费时机**: 成功返回结果后扣费
- **缓存优化**: 相同题目命中缓存不重复扣费

## 🚀 前端集成示例

### JavaScript/Fetch

#### 方式1: 请求头认证（推荐）
```javascript
async function searchQuestionWithHeaders(imageUrl, appKey, secretKey) {
  try {
    const response = await fetch('/api/v1/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-App-Key': appKey,
        'X-Secret-Key': secretKey
      },
      body: JSON.stringify({
        image_url: imageUrl
      })
    });

    const result = await response.json();

    if (result.code === 200) {
      console.log('搜题成功:', result.data);
      return result.data;
    } else {
      console.error('搜题失败:', result.message);
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
    throw error;
  }
}
```

#### 方式2: 请求体认证（简化集成）
```javascript
async function searchQuestionWithBody(imageUrl, appKey, secretKey) {
  try {
    const response = await fetch('/api/v1/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        app_key: appKey,
        secret_key: secretKey,
        image_url: imageUrl
      })
    });

    const result = await response.json();

    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    throw error;
  }
}
```

#### 方式3: 嵌套认证（结构化）
```javascript
async function searchQuestionWithAuth(imageUrl, appKey, secretKey) {
  try {
    const response = await fetch('/api/v1/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        auth: {
          app_key: appKey,
          secret_key: secretKey
        },
        image_url: imageUrl
      })
    });

    const result = await response.json();
    return result.code === 200 ? result.data : Promise.reject(new Error(result.message));
  } catch (error) {
    throw error;
  }
}
```

#### 通用调用函数
```javascript
// 支持多种认证方式的通用函数
async function searchQuestion(imageUrl, appKey, secretKey, authMethod = 'header') {
  const methods = {
    header: searchQuestionWithHeaders,
    body: searchQuestionWithBody,
    auth: searchQuestionWithAuth
  };

  const searchFunc = methods[authMethod] || methods.header;
  return await searchFunc(imageUrl, appKey, secretKey);
}

// 使用示例
searchQuestion(
  'https://example.com/question.jpg',
  'app_1234567890abcdef',
  'sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
  'body' // 使用请求体认证
).then(data => {
  // 处理搜题结果
  displayQuestion(data);
}).catch(error => {
  // 处理错误
  showError(error.message);
});
```

### Axios
```javascript
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:8080',
  timeout: 30000
});

async function searchQuestion(imageUrl, appKey, secretKey) {
  try {
    const response = await api.post('/api/v1/api/search', {
      image_url: imageUrl
    }, {
      headers: {
        'X-App-Key': appKey,
        'X-Secret-Key': secretKey
      }
    });
    
    return response.data.data;
  } catch (error) {
    if (error.response) {
      throw new Error(error.response.data.message);
    } else {
      throw new Error('网络请求失败');
    }
  }
}
```

### React Hook 示例
```javascript
import { useState, useCallback } from 'react';

export function usePhotoSearch() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  const searchQuestion = useCallback(async (imageUrl, appKey, secretKey) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/v1/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-App-Key': appKey,
          'X-Secret-Key': secretKey
        },
        body: JSON.stringify({ image_url: imageUrl })
      });
      
      const data = await response.json();
      
      if (data.code === 200) {
        setResult(data.data);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('请求失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  }, []);

  return { searchQuestion, loading, result, error };
}
```

## 📱 移动端集成

### React Native
```javascript
import { useState } from 'react';

export function usePhotoSearch() {
  const [loading, setLoading] = useState(false);

  const searchQuestion = async (imageUrl, appKey, secretKey) => {
    setLoading(true);

    try {
      const response = await fetch('http://your-api-domain.com/api/v1/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-App-Key': appKey,
          'X-Secret-Key': secretKey
        },
        body: JSON.stringify({ image_url: imageUrl })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return { searchQuestion, loading };
}
```

### Flutter/Dart
```dart
import 'dart:convert';
import 'package:http/http.dart' as http;

class PhotoSearchService {
  static const String baseUrl = 'http://your-api-domain.com';

  static Future<Map<String, dynamic>> searchQuestion(
    String imageUrl,
    String appKey,
    String secretKey,
  ) async {
    final response = await http.post(
      Uri.parse('$baseUrl/api/v1/api/search'),
      headers: {
        'Content-Type': 'application/json',
        'X-App-Key': appKey,
        'X-Secret-Key': secretKey,
      },
      body: jsonEncode({
        'image_url': imageUrl,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('搜题失败: ${response.statusCode}');
    }
  }
}
```

## 🔧 最佳实践

### 1. 错误处理
```javascript
function handleSearchError(error, code) {
  switch (code) {
    case 401:
      // 密钥无效，引导用户重新配置
      showMessage('API密钥无效，请检查配置');
      break;
    case 402:
      // 余额不足，引导用户充值
      showRechargeDialog();
      break;
    case 429:
      // 频率限制，提示用户稍后重试
      showMessage('请求过于频繁，请稍后重试');
      break;
    default:
      showMessage(error.message || '搜题失败，请重试');
  }
}
```

### 2. 加载状态管理
```javascript
function QuestionSearch() {
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState('');

  const handleSearch = async (imageUrl) => {
    setLoading(true);
    setProgress('正在识别图片...');

    try {
      const result = await searchQuestion(imageUrl, appKey, secretKey);

      if (result.cache_hit) {
        setProgress('从缓存获取结果');
      } else {
        setProgress('AI正在解析题目...');
      }

      // 显示结果
      displayResult(result);
    } catch (error) {
      handleSearchError(error);
    } finally {
      setLoading(false);
      setProgress('');
    }
  };
}
```

### 3. 结果展示组件
```javascript
function QuestionResult({ data }) {
  const renderOptions = () => {
    if (!data.options || Object.keys(data.options).length === 0) {
      return null;
    }

    return (
      <div className="options">
        {Object.entries(data.options).map(([key, value]) => (
          <div key={key} className="option">
            <span className="option-key">{key}.</span>
            <span className="option-value">{value}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="question-result">
      <div className="question-header">
        <span className="question-type">{data.question_type}</span>
        <span className="subject">{data.subject}</span>
        <span className="difficulty">难度: {data.difficulty}/5</span>
      </div>

      <div className="question-text">
        {data.question_text}
      </div>

      {renderOptions()}

      <div className="answer">
        <h4>答案：</h4>
        <p>{data.answer}</p>
      </div>

      <div className="analysis">
        <h4>解析：</h4>
        <pre>{data.analysis}</pre>
      </div>

      <div className="meta">
        <span>处理时间: {data.process_time}ms</span>
        {data.cache_hit && <span className="cache-hit">缓存命中</span>}
      </div>
    </div>
  );
}
```

## 📊 性能优化建议

### 1. 图片预处理
- 压缩图片大小（建议1-2MB以内）
- 确保图片清晰度足够
- 避免上传过大或过小的图片

### 2. 缓存利用
- 相同题目会命中缓存，响应更快
- 缓存命中时 `process_time` 会显著降低
- 合理利用缓存可以节省费用

### 3. 请求优化
- 设置合理的超时时间（建议30秒）
- 实现请求重试机制
- 避免频繁请求同一图片

## 🔒 安全注意事项

1. **密钥保护**
   - 不要在前端代码中硬编码密钥
   - 建议通过后端代理API调用
   - 定期更换密钥

2. **图片安全**
   - 确保图片URL可公开访问
   - 避免上传敏感信息
   - 注意图片版权问题

3. **频率控制**
   - 遵守API调用频率限制
   - 实现客户端限流
   - 避免恶意调用

## 📞 技术支持

如遇到问题，请检查：
1. API密钥是否正确配置
2. 图片URL是否可访问
3. 账户余额是否充足
4. 网络连接是否正常
5. 请求格式是否正确

---

**注意**: 本API目前支持中文题目识别，其他语言支持正在开发中。
```
