# 拍照搜题API环境变量配置示例
# 复制此文件为 .env 并填入实际配置值

# 服务配置
SOLVE_API_SERVER_PORT=8080
SOLVE_API_SERVER_MODE=debug

# 数据库配置
SOLVE_API_DATABASE_MYSQL_HOST=***********
SOLVE_API_DATABASE_MYSQL_PORT=3306
SOLVE_API_DATABASE_MYSQL_USERNAME=gmdns
SOLVE_API_DATABASE_MYSQL_PASSWORD=5e7fFn3HpPfuQ6Qx42Az
SOLVE_API_DATABASE_MYSQL_DATABASE=go_solve

# Redis配置
SOLVE_API_REDIS_HOST=************
SOLVE_API_REDIS_PORT=6379
SOLVE_API_REDIS_PASSWORD=y4HY8xm8dECYmDSeaX8GC

# 短信服务配置
SOLVE_API_SMS_PROVIDER=aliyun
SOLVE_API_SMS_ACCESS_KEY_ID=LTAI5t9g26wqutn692bcCRmR
SOLVE_API_SMS_ACCESS_KEY_SECRET=******************************
SOLVE_API_SMS_SIGN_NAME=青岛果沐云
SOLVE_API_SMS_TEMPLATE_CODE=SMS_294081777

# AI模型配置
SOLVE_API_MODELS_DEEPSEEK_CHAT_API_KEY=***********************************

# 应用配置
SOLVE_API_APP_INVITE_CODE=SOLVE2024
SOLVE_API_APP_RATE_LIMIT=10
SOLVE_API_APP_CACHE_TTL=604800

# 日志配置
SOLVE_API_LOG_LEVEL=info
SOLVE_API_LOG_FILENAME=logs/app.log
