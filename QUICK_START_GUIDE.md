# 拍照搜题API快速接入指南

## 🚀 5分钟快速接入

### 1. 获取API密钥

```bash
# 1. 用户注册
curl -X POST http://localhost:8080/api/v1/user/register \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138000",
    "password": "password123",
    "code": "123456",
    "invite_code": "SOLVE2024"
  }'

# 2. 用户登录
curl -X POST http://localhost:8080/api/v1/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138000",
    "password": "password123"
  }'

# 3. 创建应用
curl -X POST http://localhost:8080/api/v1/user/1/app \
  -H "Content-Type: application/json" \
  -d '{
    "name": "我的搜题应用",
    "type": 1
  }'

# 4. 获取密钥
curl -X GET http://localhost:8080/api/v1/user/1/app/1
```

### 2. 调用搜题API

```javascript
// 基础调用示例
async function searchQuestion(imageUrl) {
  const response = await fetch('/api/v1/api/search', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-App-Key': 'your_app_key',
      'X-Secret-Key': 'your_secret_key'
    },
    body: JSON.stringify({
      image_url: imageUrl
    })
  });
  
  const result = await response.json();
  return result.data;
}

// 使用示例
searchQuestion('https://example.com/question.jpg')
  .then(data => {
    console.log('题目类型:', data.question_type);
    console.log('题目内容:', data.question_text);
    console.log('答案:', data.answer);
    console.log('解析:', data.analysis);
  })
  .catch(error => {
    console.error('搜题失败:', error);
  });
```

### 3. 完整的React组件示例

```jsx
import React, { useState } from 'react';

function PhotoSearch() {
  const [imageUrl, setImageUrl] = useState('');
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSearch = async () => {
    if (!imageUrl) {
      setError('请输入图片URL');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/v1/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-App-Key': 'your_app_key',
          'X-Secret-Key': 'your_secret_key'
        },
        body: JSON.stringify({
          image_url: imageUrl
        })
      });

      const data = await response.json();

      if (data.code === 200) {
        setResult(data.data);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('请求失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="photo-search">
      <h2>拍照搜题</h2>
      
      <div className="input-section">
        <input
          type="url"
          placeholder="请输入图片URL"
          value={imageUrl}
          onChange={(e) => setImageUrl(e.target.value)}
        />
        <button onClick={handleSearch} disabled={loading}>
          {loading ? '搜题中...' : '开始搜题'}
        </button>
      </div>

      {error && (
        <div className="error">
          错误: {error}
        </div>
      )}

      {result && (
        <div className="result">
          <h3>搜题结果</h3>
          
          <div className="question-info">
            <span className="type">{result.question_type}</span>
            <span className="subject">{result.subject}</span>
            <span className="grade">{result.grade}</span>
            <span className="difficulty">难度: {result.difficulty}/5</span>
          </div>

          <div className="question">
            <h4>题目:</h4>
            <p>{result.question_text}</p>
          </div>

          {result.options && Object.keys(result.options).length > 0 && (
            <div className="options">
              <h4>选项:</h4>
              {Object.entries(result.options).map(([key, value]) => (
                <div key={key} className="option">
                  {key}. {value}
                </div>
              ))}
            </div>
          )}

          <div className="answer">
            <h4>答案:</h4>
            <p>{result.answer}</p>
          </div>

          <div className="analysis">
            <h4>解析:</h4>
            <pre>{result.analysis}</pre>
          </div>

          <div className="meta">
            <small>
              处理时间: {result.process_time}ms
              {result.cache_hit && ' (缓存命中)'}
            </small>
          </div>
        </div>
      )}
    </div>
  );
}

export default PhotoSearch;
```

### 4. CSS样式参考

```css
.photo-search {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.input-section {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.input-section input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.input-section button {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.input-section button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error {
  color: #dc3545;
  background: #f8d7da;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.result {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
}

.question-info {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.question-info span {
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.question, .options, .answer, .analysis {
  margin-bottom: 15px;
}

.question h4, .options h4, .answer h4, .analysis h4 {
  margin: 0 0 8px 0;
  color: #333;
}

.option {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.analysis pre {
  white-space: pre-wrap;
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  font-family: inherit;
}

.meta {
  color: #666;
  font-size: 12px;
  text-align: right;
}
```

## 📋 API响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "搜题成功",
  "data": {
    "question_type": "单选题",
    "question_text": "题目内容",
    "options": {"A": "选项A", "B": "选项B"},
    "answer": "A",
    "analysis": "详细解析",
    "subject": "数学",
    "grade": "高中",
    "difficulty": 3,
    "cache_hit": false,
    "process_time": 1500
  }
}
```

### 错误响应
```json
{
  "code": 401,
  "message": "API密钥无效",
  "data": null
}
```

## ⚠️ 常见错误

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 参数错误 | 检查图片URL格式 |
| 401 | 密钥无效 | 检查API密钥配置 |
| 402 | 余额不足 | 用户需要充值 |
| 429 | 请求过频 | 降低请求频率 |
| 500 | 服务错误 | 稍后重试或联系技术支持 |

## 💡 开发提示

1. **测试图片**: 使用清晰的题目图片进行测试
2. **错误处理**: 实现完善的错误处理机制
3. **加载状态**: 提供良好的用户体验
4. **缓存利用**: 相同题目会命中缓存，响应更快
5. **费用控制**: 实现客户端限流避免过度消费

## 📞 技术支持

- 详细文档: `PHOTO_SEARCH_API_GUIDE.md`
- 测试脚本: `test_refactored_api.sh`
- 服务状态: `http://localhost:8080/health`

开始您的拍照搜题之旅吧！🎯
