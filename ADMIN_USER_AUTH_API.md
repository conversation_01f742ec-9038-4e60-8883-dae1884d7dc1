# 用户应用创建业务API接口文档

## 概述

本文档详细说明了用户应用创建、管理、配置等相关API接口，为前端开发团队提供完整的接入指南。系统支持多应用管理，每个应用拥有独立的API密钥，用于调用拍照搜题等核心业务功能。

## 基础信息

- **服务地址**: `http://localhost:8080`
- **API版本**: v1
- **内容类型**: `application/json`
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

### 状态码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权/认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 用户认证接口

### 1. 用户注册

**接口地址**: `POST /api/v1/user/register`

**请求参数**:
```json
{
  "phone": "13800138000",
  "password": "password123",
  "code": "123456",
  "invite_code": "SOLVE2024"
}
```

**参数说明**:
- `phone`: 手机号，11位数字
- `password`: 密码，6-20位，支持大小写字母、数字、常用标点符号
- `code`: 短信验证码，6位数字
- `invite_code`: 邀请码，默认为"SOLVE2024"

**响应示例**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 0,
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2. 用户登录

**接口地址**: `POST /api/v1/user/login`

**请求参数**:
```json
{
  "phone": "13800138000",
  "password": "password123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 100.50,
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 发送注册验证码

**接口地址**: `POST /api/v1/user/send-code`

**请求参数**:
```json
{
  "phone": "13800138000"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": null
}
```

## 应用管理接口

### 1. 创建应用

**接口地址**: `POST /api/v1/user/{user_id}/app`

**请求参数**:
```json
{
  "name": "我的搜题应用",
  "type": 1
}
```

**参数说明**:
- `name`: 应用名称，1-50个字符
- `type`: 应用类型，1=拍照搜题

**响应示例**:
```json
{
  "code": 200,
  "message": "应用创建成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "app_1234567890abcdef",
    "secret_key": "sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

**错误响应**:
- `403`: 账户已被冻结，无法创建应用
- `409`: 每个用户最多只能创建5个应用

### 2. 获取应用列表

**接口地址**: `GET /api/v1/user/{user_id}/app`

**响应示例**:
```json
{
  "code": 200,
  "message": "获取应用列表成功",
  "data": [
    {
      "id": 1,
      "name": "我的搜题应用",
      "type": 1,
      "app_key": "app_1234567890abcdef",
      "status": 1,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

**注意**: 列表接口不返回`secret_key`，需要通过详情接口获取

### 3. 获取应用详情

**接口地址**: `GET /api/v1/user/{user_id}/app/{app_id}`

**响应示例**:
```json
{
  "code": 200,
  "message": "获取应用详情成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "app_1234567890abcdef",
    "secret_key": "sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 4. 更新应用信息

**接口地址**: `PUT /api/v1/user/{user_id}/app/{app_id}`

**请求参数**:
```json
{
  "name": "新的应用名称"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "应用信息更新成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "新的应用名称",
    "type": 1,
    "app_key": "app_1234567890abcdef",
    "secret_key": "sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 5. 重置应用密钥

**接口地址**: `PUT /api/v1/user/{user_id}/app/{app_id}/reset-secret`

**请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "SecretKey重置成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "app_1234567890abcdef",
    "secret_key": "sk_newkey1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 6. 更新应用状态

**接口地址**: `PUT /api/v1/user/{user_id}/app/{app_id}/status`

**请求参数**:
```json
{
  "status": 2
}
```

**参数说明**:
- `status`: 应用状态，1=正常，2=冻结

**响应示例**:
```json
{
  "code": 200,
  "message": "应用状态更新成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "app_1234567890abcdef",
    "secret_key": "sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
    "status": 2,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

## 拍照搜题接口

### 1. 拍照搜题

**接口地址**: `POST /api/v1/api/search`

**请求头**:
```
Content-Type: application/json
X-App-Key: app_1234567890abcdef
X-Secret-Key: sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890
```

**请求参数**:
```json
{
  "image_url": "https://example.com/image.jpg"
}
```

**参数说明**:
- `image_url`: 图片URL地址，支持常见图片格式（jpg、png、gif、webp等）

**响应示例**:
```json
{
  "code": 200,
  "message": "搜题成功",
  "data": {
    "id": 1,
    "question": "解这个方程：2x + 3 = 7",
    "answer": "解：2x + 3 = 7\n2x = 7 - 3\n2x = 4\nx = 2",
    "analysis": "这是一个一元一次方程，通过移项和化简可以求解。",
    "cost": 0.1,
    "cached": false,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

**错误响应**:
- `401`: API密钥无效或应用已冻结
- `402`: 余额不足
- `400`: 图片URL格式错误或无法访问

## 用户余额管理接口

### 1. 获取用户余额

**接口地址**: `GET /api/v1/user/{user_id}/balance`

**响应示例**:
```json
{
  "code": 200,
  "message": "获取余额成功",
  "data": {
    "balance": 100.50
  }
}
```

### 2. 用户充值

**接口地址**: `POST /api/v1/user/{user_id}/recharge`

**请求参数**:
```json
{
  "amount": 50.00,
  "description": "支付宝充值"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "充值成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "amount": 50.00,
    "balance": 150.50,
    "type": 1,
    "description": "支付宝充值",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 获取余额变动记录

**接口地址**: `GET /api/v1/user/{user_id}/balance/logs`

**查询参数**:
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20
- `type`: 记录类型，1=充值，2=消费，3=退款

**响应示例**:
```json
{
  "code": 200,
  "message": "获取余额记录成功",
  "data": {
    "list": [
      {
        "id": 1,
        "amount": -0.1,
        "balance": 100.40,
        "type": 2,
        "description": "拍照搜题扣费",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 20
  }
}
```

## 应用类型和状态说明

### 应用类型
- `1`: 拍照搜题 - 支持图片识别和题目解析

### 应用状态
- `1`: 正常 - 可以正常调用API
- `2`: 冻结 - 无法调用API，需要联系管理员

### 余额记录类型
- `1`: 充值 - 用户充值增加余额
- `2`: 消费 - 调用API扣费
- `3`: 退款 - 管理员退款

## 业务流程说明

### 1. 用户注册流程
1. 发送验证码 → `POST /api/v1/user/send-code`
2. 用户注册 → `POST /api/v1/user/register`
3. 用户登录 → `POST /api/v1/user/login`

### 2. 应用创建流程
1. 用户登录获取user_id
2. 创建应用 → `POST /api/v1/user/{user_id}/app`
3. 获取应用详情（包含密钥） → `GET /api/v1/user/{user_id}/app/{app_id}`

### 3. 拍照搜题流程
1. 确保用户余额充足 → `GET /api/v1/user/{user_id}/balance`
2. 使用应用密钥调用搜题API → `POST /api/v1/api/search`
3. 系统自动扣费并返回结果

### 4. 密钥管理流程
1. 查看当前密钥 → `GET /api/v1/user/{user_id}/app/{app_id}`
2. 重置密钥（如果泄露） → `PUT /api/v1/user/{user_id}/app/{app_id}/reset-secret`
3. 更新应用中的密钥配置

## 错误处理

### 常见错误码

#### 400 错误 - 请求参数错误
```json
{
  "code": 400,
  "message": "应用名称不能为空",
  "data": null
}
```

#### 401 错误 - 认证失败
```json
{
  "code": 401,
  "message": "API密钥无效",
  "data": null
}
```

#### 402 错误 - 余额不足
```json
{
  "code": 402,
  "message": "余额不足，请先充值",
  "data": null
}
```

#### 403 错误 - 权限不足
```json
{
  "code": 403,
  "message": "账户已被冻结，无法创建应用",
  "data": null
}
```

#### 404 错误 - 资源不存在
```json
{
  "code": 404,
  "message": "应用不存在",
  "data": null
}
```

#### 409 错误 - 资源冲突
```json
{
  "code": 409,
  "message": "每个用户最多只能创建5个应用",
  "data": null
}
```

## 限制说明

### 应用限制
- 每个用户最多创建 **5个应用**
- 应用名称长度：**1-50个字符**
- 目前仅支持拍照搜题类型应用

### API调用限制
- 默认限流：**10次/秒**
- 图片大小：建议不超过 **10MB**
- 支持格式：jpg、png、gif、webp等常见格式

### 余额限制
- 最小充值金额：**1元**
- 拍照搜题费用：**0.1元/次**（具体价格可能调整）

## 开发调试

### 启动服务
```bash
# 开发模式启动
./dev.sh

# 或者直接运行
go run cmd/main.go
```

### 测试接口示例

#### 1. 创建应用
```bash
curl -X POST http://localhost:8080/api/v1/user/1/app \
  -H "Content-Type: application/json" \
  -d '{"name":"测试应用","type":1}'
```

#### 2. 获取应用列表
```bash
curl -X GET http://localhost:8080/api/v1/user/1/app
```

#### 3. 拍照搜题
```bash
curl -X POST http://localhost:8080/api/v1/api/search \
  -H "Content-Type: application/json" \
  -H "X-App-Key: app_1234567890abcdef" \
  -H "X-Secret-Key: sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890" \
  -d '{"image_url":"https://example.com/image.jpg"}'
```

#### 4. 用户充值
```bash
curl -X POST http://localhost:8080/api/v1/user/1/recharge \
  -H "Content-Type: application/json" \
  -d '{"amount":50.00,"description":"测试充值"}'
```

## 前端集成建议

### 1. 状态管理
- 保存用户登录状态和user_id
- 缓存应用列表，避免频繁请求
- 实时显示用户余额

### 2. 错误处理
- 统一处理API错误响应
- 余额不足时引导用户充值
- 应用被冻结时显示相应提示

### 3. 安全考虑
- 妥善保管应用密钥，不要在前端明文存储
- 建议在后端代理API调用，避免密钥泄露
- 定期检查应用状态

### 4. 用户体验
- 创建应用后自动跳转到应用详情
- 提供密钥重置功能的安全提示
- 显示应用使用统计和余额消费记录
