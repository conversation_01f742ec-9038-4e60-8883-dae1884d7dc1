#!/bin/bash

# 应用管理API测试脚本
# 测试应用管理模块的所有功能

BASE_URL="http://localhost:8080"
USER_ID="1"  # 假设用户ID为1，需要先注册用户

echo "=== 应用管理API测试 ==="
echo "基础URL: $BASE_URL"
echo "测试用户ID: $USER_ID"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    
    echo -e "${YELLOW}测试: $description${NC}"
    echo "请求: $method $url"
    if [ ! -z "$data" ]; then
        echo "数据: $data"
    fi
    
    if [ ! -z "$data" ]; then
        response=$(curl -s -X $method "$url" \
            -H "Content-Type: application/json" \
            -d "$data")
    else
        response=$(curl -s -X $method "$url")
    fi
    
    echo "响应: $response"
    
    # 检查响应是否包含成功标识
    if echo "$response" | grep -q '"code":200'; then
        echo -e "${GREEN}✓ 测试通过${NC}"
    else
        echo -e "${RED}✗ 测试失败${NC}"
    fi
    echo "----------------------------------------"
}

# 1. 创建应用
echo -e "${YELLOW}1. 创建应用测试${NC}"
test_api "POST" "$BASE_URL/api/v1/user/$USER_ID/app" \
    '{"name":"我的搜题应用","type":1}' \
    "创建拍照搜题应用"

test_api "POST" "$BASE_URL/api/v1/user/$USER_ID/app" \
    '{"name":"测试应用2","type":1}' \
    "创建第二个应用"

# 2. 获取应用列表
echo -e "${YELLOW}2. 获取应用列表测试${NC}"
test_api "GET" "$BASE_URL/api/v1/user/$USER_ID/app" \
    "" \
    "获取用户应用列表"

# 3. 获取应用详情
echo -e "${YELLOW}3. 获取应用详情测试${NC}"
test_api "GET" "$BASE_URL/api/v1/user/$USER_ID/app/1" \
    "" \
    "获取应用ID为1的详情"

# 4. 更新应用信息
echo -e "${YELLOW}4. 更新应用信息测试${NC}"
test_api "PUT" "$BASE_URL/api/v1/user/$USER_ID/app/1" \
    '{"name":"更新后的应用名称"}' \
    "更新应用名称"

# 5. 重置SecretKey
echo -e "${YELLOW}5. 重置SecretKey测试${NC}"
test_api "PUT" "$BASE_URL/api/v1/user/$USER_ID/app/1/reset-secret" \
    "" \
    "重置应用SecretKey"

# 6. 更新应用状态
echo -e "${YELLOW}6. 更新应用状态测试${NC}"
test_api "PUT" "$BASE_URL/api/v1/user/$USER_ID/app/1/status" \
    '{"status":2}' \
    "冻结应用"

test_api "PUT" "$BASE_URL/api/v1/user/$USER_ID/app/1/status" \
    '{"status":1}' \
    "恢复应用"

# 7. 错误情况测试
echo -e "${YELLOW}7. 错误情况测试${NC}"

# 测试无效的用户ID
test_api "GET" "$BASE_URL/api/v1/user/999/app" \
    "" \
    "测试不存在的用户ID"

# 测试无效的应用ID
test_api "GET" "$BASE_URL/api/v1/user/$USER_ID/app/999" \
    "" \
    "测试不存在的应用ID"

# 测试无效的应用类型
test_api "POST" "$BASE_URL/api/v1/user/$USER_ID/app" \
    '{"name":"无效类型应用","type":99}' \
    "测试无效的应用类型"

# 测试空的应用名称
test_api "POST" "$BASE_URL/api/v1/user/$USER_ID/app" \
    '{"name":"","type":1}' \
    "测试空的应用名称"

# 测试应用数量限制（创建多个应用）
echo -e "${YELLOW}8. 测试应用数量限制${NC}"
for i in {3..6}; do
    test_api "POST" "$BASE_URL/api/v1/user/$USER_ID/app" \
        "{\"name\":\"测试应用$i\",\"type\":1}" \
        "创建第$i个应用"
done

# 尝试创建第6个应用（应该失败，因为限制是5个）
test_api "POST" "$BASE_URL/api/v1/user/$USER_ID/app" \
    '{"name":"第6个应用","type":1}' \
    "尝试创建第6个应用（应该失败）"

echo ""
echo -e "${GREEN}=== 应用管理API测试完成 ===${NC}"
echo ""
echo "注意事项："
echo "1. 确保服务器正在运行在 $BASE_URL"
echo "2. 确保用户ID $USER_ID 存在（需要先注册用户）"
echo "3. 某些测试可能会因为数据库状态而失败"
echo "4. 应用数量限制默认为5个"
