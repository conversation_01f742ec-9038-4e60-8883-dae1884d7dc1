package main

import (
	"fmt"
	"log"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/model"
)

func main() {
	fmt.Println("=== 启动测试 ===")

	// 1. 加载配置
	fmt.Println("1. 加载配置...")
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("配置加载失败: %v", err)
	}
	fmt.Printf("✅ 配置加载成功\n")

	// 2. 初始化MySQL
	fmt.Println("2. 初始化MySQL...")
	err = database.InitMySQL(&cfg.Database.MySQL)
	if err != nil {
		log.Fatalf("MySQL初始化失败: %v", err)
	}
	fmt.Printf("✅ MySQL连接成功\n")

	// 3. 初始化Redis
	fmt.Println("3. 初始化Redis...")
	err = database.InitRedis(&cfg.Redis)
	if err != nil {
		log.Fatalf("Redis初始化失败: %v", err)
	}
	fmt.Printf("✅ Redis连接成功\n")

	// 4. 数据库迁移
	fmt.Println("4. 执行数据库迁移...")
	db := database.GetDB()
	err = db.AutoMigrate(
		&model.User{},
		&model.SystemConfig{},
		&model.Admin{},
	)
	if err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}
	fmt.Printf("✅ 数据库迁移成功\n")

	// 5. 检查表
	fmt.Println("5. 检查数据库表...")
	tables := []string{"users", "system_configs", "admins"}
	for _, table := range tables {
		var count int64
		err := db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?", table).Scan(&count).Error
		if err != nil {
			fmt.Printf("❌ 检查表 %s 失败: %v\n", table, err)
		} else if count > 0 {
			fmt.Printf("✅ 表 %s 存在\n", table)
		} else {
			fmt.Printf("❌ 表 %s 不存在\n", table)
		}
	}

	fmt.Println("=== 测试完成 ===")
}
