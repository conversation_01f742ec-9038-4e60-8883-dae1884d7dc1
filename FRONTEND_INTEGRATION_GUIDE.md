# 拍照搜题API前端快速接入指南

## 概述

本文档为前端开发团队提供快速接入拍照搜题API服务的完整指南，包括接口说明、认证方式、示例代码和最佳实践。

## 基础信息

- **API基础URL**: `http://your-domain.com/api/v1`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求方式**: HTTP/HTTPS

## 快速开始

### 1. 用户注册流程

#### 步骤1: 发送验证码
```javascript
// 发送短信验证码
const sendCode = async (phone) => {
  const response = await fetch('/api/v1/user/send-code', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      phone: phone
    })
  });
  return response.json();
};

// 使用示例
sendCode('15653259315');
```

#### 步骤2: 用户注册
```javascript
// 用户注册
const register = async (phone, password, code) => {
  const response = await fetch('/api/v1/user/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      phone: phone,
      password: password,
      code: code,
      invite_code: 'SOLVE2024'  // 邀请码
    })
  });
  return response.json();
};

// 使用示例
register('15653259315', '123456', '123456');
```

#### 步骤3: 用户登录
```javascript
// 用户登录
const login = async (phone, password) => {
  const response = await fetch('/api/v1/user/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      phone: phone,
      password: password
    })
  });
  return response.json();
};

// 使用示例
const loginResult = await login('15653259315', '123456');
const userId = loginResult.data.id;
```

### 2. 应用管理流程

#### 创建应用
```javascript
// 创建应用获取API密钥
const createApp = async (userId, appName) => {
  const response = await fetch(`/api/v1/user/${userId}/app`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      name: appName,
      type: 1  // 1: 拍照搜题应用
    })
  });
  return response.json();
};

// 使用示例
const appResult = await createApp(userId, '我的搜题应用');
const appKey = appResult.data.app_key;
const secretKey = appResult.data.secret_key;
```

#### 获取应用列表
```javascript
// 获取用户的应用列表
const getApps = async (userId) => {
  const response = await fetch(`/api/v1/user/${userId}/app`);
  return response.json();
};
```

### 3. 拍照搜题核心功能

#### 搜题接口调用
```javascript
// 拍照搜题 - 核心功能
const searchQuestion = async (imageUrl, appKey, secretKey) => {
  const response = await fetch('/api/v1/api/search', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-App-Key': appKey,
      'X-Secret-Key': secretKey
    },
    body: JSON.stringify({
      image_url: imageUrl
    })
  });
  return response.json();
};

// 使用示例
const searchResult = await searchQuestion(
  'https://example.com/question.jpg',
  appKey,
  secretKey
);

// 处理搜题结果
if (searchResult.code === 200) {
  const question = searchResult.data;
  console.log('题目内容:', question.content);
  console.log('解析过程:', question.analysis);
  console.log('答案:', question.answer);
  console.log('学科:', question.subject);
  console.log('年级:', question.grade);
  console.log('难度:', question.difficulty);
  console.log('是否命中缓存:', question.cache_hit);
  console.log('处理时间:', question.process_time + 'ms');
}
```

### 4. 余额管理

#### 查询余额
```javascript
// 查询用户余额
const getBalance = async (userId) => {
  const response = await fetch(`/api/v1/user/${userId}/balance`);
  return response.json();
};

// 使用示例
const balanceResult = await getBalance(userId);
const currentBalance = balanceResult.data.balance;
```

#### 充值
```javascript
// 用户充值
const recharge = async (userId, amount, description) => {
  const response = await fetch(`/api/v1/user/${userId}/recharge`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      amount: amount,
      description: description || '账户充值'
    })
  });
  return response.json();
};

// 使用示例
recharge(userId, 10.00, '支付宝充值');
```

#### 余额日志
```javascript
// 获取余额变动日志
const getBalanceLogs = async (userId, page = 1, pageSize = 10, type = null) => {
  let url = `/api/v1/user/${userId}/balance/logs?page=${page}&page_size=${pageSize}`;
  if (type) {
    url += `&type=${type}`;  // 1:充值 2:消费 3:退款
  }
  
  const response = await fetch(url);
  return response.json();
};
```

## 完整的前端集成示例

### React组件示例

```jsx
import React, { useState, useEffect } from 'react';

const QuestionSearchApp = () => {
  const [user, setUser] = useState(null);
  const [app, setApp] = useState(null);
  const [balance, setBalance] = useState(0);
  const [searchResult, setSearchResult] = useState(null);
  const [loading, setLoading] = useState(false);

  // 用户登录
  const handleLogin = async (phone, password) => {
    try {
      const result = await login(phone, password);
      if (result.code === 200) {
        setUser(result.data);
        // 获取用户应用
        const appsResult = await getApps(result.data.id);
        if (appsResult.data.length > 0) {
          setApp(appsResult.data[0]);
        }
        // 获取余额
        const balanceResult = await getBalance(result.data.id);
        setBalance(balanceResult.data.balance);
      }
    } catch (error) {
      console.error('登录失败:', error);
    }
  };

  // 搜题功能
  const handleSearch = async (imageUrl) => {
    if (!app) {
      alert('请先创建应用');
      return;
    }

    setLoading(true);
    try {
      const result = await searchQuestion(imageUrl, app.app_key, app.secret_key);
      if (result.code === 200) {
        setSearchResult(result.data);
        // 更新余额
        const balanceResult = await getBalance(user.id);
        setBalance(balanceResult.data.balance);
      } else if (result.code === 402) {
        alert('余额不足，请先充值');
      } else {
        alert('搜题失败: ' + result.message);
      }
    } catch (error) {
      console.error('搜题失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="question-search-app">
      <div className="header">
        <h1>拍照搜题</h1>
        {user && (
          <div className="user-info">
            <span>用户: {user.phone}</span>
            <span>余额: ¥{balance.toFixed(2)}</span>
          </div>
        )}
      </div>

      <div className="search-section">
        <input
          type="text"
          placeholder="请输入图片URL"
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              handleSearch(e.target.value);
            }
          }}
        />
        <button onClick={() => handleSearch(document.querySelector('input').value)} disabled={loading}>
          {loading ? '搜题中...' : '开始搜题'}
        </button>
      </div>

      {searchResult && (
        <div className="result-section">
          <h3>搜题结果</h3>
          <div className="question-content">
            <p><strong>题目:</strong> {searchResult.content}</p>
            <p><strong>解析:</strong> {searchResult.analysis}</p>
            <p><strong>答案:</strong> {searchResult.answer}</p>
            <p><strong>学科:</strong> {searchResult.subject}</p>
            <p><strong>年级:</strong> {searchResult.grade}</p>
            <p><strong>难度:</strong> {searchResult.difficulty}/5</p>
            <p><strong>处理时间:</strong> {searchResult.process_time}ms</p>
            {searchResult.cache_hit && <p className="cache-hit">✓ 缓存命中</p>}
          </div>
        </div>
      )}
    </div>
  );
};

export default QuestionSearchApp;
```

### Vue.js组件示例

```vue
<template>
  <div class="question-search-app">
    <div class="header">
      <h1>拍照搜题</h1>
      <div v-if="user" class="user-info">
        <span>用户: {{ user.phone }}</span>
        <span>余额: ¥{{ balance.toFixed(2) }}</span>
      </div>
    </div>

    <div class="search-section">
      <input
        v-model="imageUrl"
        type="text"
        placeholder="请输入图片URL"
        @keyup.enter="handleSearch"
      />
      <button @click="handleSearch" :disabled="loading">
        {{ loading ? '搜题中...' : '开始搜题' }}
      </button>
    </div>

    <div v-if="searchResult" class="result-section">
      <h3>搜题结果</h3>
      <div class="question-content">
        <p><strong>题目:</strong> {{ searchResult.content }}</p>
        <p><strong>解析:</strong> {{ searchResult.analysis }}</p>
        <p><strong>答案:</strong> {{ searchResult.answer }}</p>
        <p><strong>学科:</strong> {{ searchResult.subject }}</p>
        <p><strong>年级:</strong> {{ searchResult.grade }}</p>
        <p><strong>难度:</strong> {{ searchResult.difficulty }}/5</p>
        <p><strong>处理时间:</strong> {{ searchResult.process_time }}ms</p>
        <p v-if="searchResult.cache_hit" class="cache-hit">✓ 缓存命中</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuestionSearchApp',
  data() {
    return {
      user: null,
      app: null,
      balance: 0,
      imageUrl: '',
      searchResult: null,
      loading: false
    };
  },
  methods: {
    async handleSearch() {
      if (!this.app) {
        alert('请先创建应用');
        return;
      }

      this.loading = true;
      try {
        const result = await searchQuestion(this.imageUrl, this.app.app_key, this.app.secret_key);
        if (result.code === 200) {
          this.searchResult = result.data;
          // 更新余额
          const balanceResult = await getBalance(this.user.id);
          this.balance = balanceResult.data.balance;
        } else if (result.code === 402) {
          alert('余额不足，请先充值');
        } else {
          alert('搜题失败: ' + result.message);
        }
      } catch (error) {
        console.error('搜题失败:', error);
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>
```

## 错误处理

### 常见错误码处理

```javascript
const handleApiResponse = (response) => {
  switch (response.code) {
    case 200:
      return response.data;
    case 400:
      throw new Error('请求参数错误: ' + response.message);
    case 401:
      throw new Error('API密钥无效，请检查认证信息');
    case 402:
      throw new Error('余额不足，请先充值');
    case 403:
      throw new Error('账户被冻结，请联系客服');
    case 429:
      throw new Error('请求过于频繁，请稍后再试');
    case 500:
      throw new Error('服务器内部错误，请稍后重试');
    default:
      throw new Error('未知错误: ' + response.message);
  }
};
```

## 最佳实践

### 1. API密钥安全
- 不要在前端代码中硬编码API密钥
- 使用环境变量或配置文件管理密钥
- 定期轮换API密钥

### 2. 错误处理
- 实现统一的错误处理机制
- 为用户提供友好的错误提示
- 记录错误日志便于调试

### 3. 性能优化
- 实现请求缓存机制
- 使用防抖处理频繁请求
- 显示加载状态提升用户体验

### 4. 用户体验
- 实时显示余额变化
- 提供搜题历史记录
- 支持图片预览功能

## 测试建议

### 1. 功能测试
- 测试完整的用户注册登录流程
- 测试应用创建和密钥管理
- 测试搜题功能的各种场景

### 2. 异常测试
- 测试网络异常情况
- 测试API密钥错误情况
- 测试余额不足情况

### 3. 性能测试
- 测试并发请求处理
- 测试大图片处理性能
- 测试缓存命中率

## 技术支持

如果在接入过程中遇到问题，请联系技术支持团队：
- 邮箱: <EMAIL>
- 技术文档: https://docs.example.com
- 问题反馈: https://github.com/example/solve_api/issues
