# 前端接入文档总览

## 📚 文档导航

| 文档名称 | 用途 | 适用场景 |
|----------|------|----------|
| [FRONTEND_API_INTEGRATION_GUIDE.md](./FRONTEND_API_INTEGRATION_GUIDE.md) | 完整的前端API接入指南 | 全栈开发团队 |
| [CONFIG_MANAGEMENT_API_GUIDE.md](./CONFIG_MANAGEMENT_API_GUIDE.md) | 配置管理API专门指南 | 管理后台开发 |
| [PHOTO_SEARCH_API_GUIDE.md](./PHOTO_SEARCH_API_GUIDE.md) | 拍照搜题API详细文档 | 核心业务开发 |
| [QUICK_START_GUIDE.md](./QUICK_START_GUIDE.md) | 5分钟快速接入指南 | 快速上手 |
| [api_test_page.html](./api_test_page.html) | 在线API测试页面 | 接口测试 |

## 🎯 核心功能模块

### 1. 拍照搜题API 📸

**核心接口**: `POST /api/v1/api/search`

**三种认证方式**:
- 请求头认证（推荐）
- 请求体认证（简化集成）
- 嵌套认证（结构化）

**快速示例**:
```javascript
// 请求体认证（最简单）
const searchQuestion = async (imageUrl, appKey, secretKey) => {
  const response = await fetch('/api/v1/api/search', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      app_key: appKey,
      secret_key: secretKey,
      image_url: imageUrl
    })
  });
  return response.json();
};
```

### 2. 配置管理API ⚙️

**主要功能**:
- 模型配置管理（AI模型参数配置）
- 价格配置管理（服务定价设置）
- 系统配置管理（系统参数调整）

**权限要求**: 需要管理员token

**快速示例**:
```javascript
// 获取模型配置列表
const getModels = async () => {
  const response = await fetch('/api/v1/admin/model', {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

## 🔄 完整业务流程

### 用户端业务流程
```
1. 用户注册/登录
   ↓
2. 创建应用获取API密钥
   ↓
3. 使用API密钥调用拍照搜题
   ↓
4. 查看余额和使用记录
```

### 管理端业务流程
```
1. 管理员登录
   ↓
2. 配置AI模型参数
   ↓
3. 设置服务价格
   ↓
4. 监控系统状态
   ↓
5. 查看统计数据
```

## 📱 前端技术栈支持

### React 项目集成
```bash
# 安装依赖
npm install axios

# 创建API服务
// services/api.js
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:8080/api/v1',
  timeout: 30000
});

export const photoSearch = {
  search: (imageUrl, appKey, secretKey) => 
    api.post('/api/search', {
      app_key: appKey,
      secret_key: secretKey,
      image_url: imageUrl
    })
};
```

### Vue.js 项目集成
```bash
# 安装依赖
npm install axios

# 创建API插件
// plugins/api.js
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:8080/api/v1'
});

export default {
  install(Vue) {
    Vue.prototype.$api = {
      searchQuestion: (imageUrl, appKey, secretKey) =>
        api.post('/api/search', {
          app_key: appKey,
          secret_key: secretKey,
          image_url: imageUrl
        })
    };
  }
};
```

### Angular 项目集成
```typescript
// services/api.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private baseUrl = 'http://localhost:8080/api/v1';

  constructor(private http: HttpClient) {}

  searchQuestion(imageUrl: string, appKey: string, secretKey: string) {
    return this.http.post(`${this.baseUrl}/api/search`, {
      app_key: appKey,
      secret_key: secretKey,
      image_url: imageUrl
    });
  }
}
```

## 📊 数据格式规范

### 成功响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 错误响应格式
```json
{
  "code": 400,
  "message": "错误描述",
  "data": null
}
```

### 常见错误码
| 错误码 | 含义 | 处理建议 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查参数格式 |
| 401 | 认证失败 | 检查API密钥 |
| 402 | 余额不足 | 引导用户充值 |
| 403 | 权限不足 | 检查账户状态 |
| 429 | 请求过频 | 实现限流重试 |
| 500 | 服务器错误 | 稍后重试 |

## 🔧 开发工具

### 1. API测试工具
- **在线测试页面**: `api_test_page.html`
- **Postman集合**: 可导入API文档生成
- **curl命令**: 文档中提供完整示例

### 2. 调试工具
```javascript
// 开启调试模式
const DEBUG = true;

const apiCall = async (url, options) => {
  if (DEBUG) {
    console.log('API请求:', url, options);
  }
  
  const response = await fetch(url, options);
  const data = await response.json();
  
  if (DEBUG) {
    console.log('API响应:', data);
  }
  
  return data;
};
```

### 3. 错误处理工具
```javascript
// 统一错误处理
const handleApiError = (error, code) => {
  const errorMessages = {
    400: '请求参数错误，请检查输入',
    401: 'API密钥无效，请重新配置',
    402: '余额不足，请先充值',
    403: '账户被冻结，请联系客服',
    429: '请求过于频繁，请稍后重试',
    500: '服务器错误，请稍后重试'
  };
  
  return errorMessages[code] || error.message || '未知错误';
};
```

## 🚀 部署建议

### 开发环境
```javascript
const config = {
  apiBaseUrl: 'http://localhost:8080/api/v1',
  timeout: 30000,
  debug: true
};
```

### 生产环境
```javascript
const config = {
  apiBaseUrl: 'https://your-domain.com/api/v1',
  timeout: 10000,
  debug: false
};
```

### 环境变量配置
```bash
# .env.development
REACT_APP_API_BASE_URL=http://localhost:8080/api/v1
REACT_APP_DEBUG=true

# .env.production
REACT_APP_API_BASE_URL=https://your-domain.com/api/v1
REACT_APP_DEBUG=false
```

## 📞 技术支持

### 常见问题
1. **Q**: 如何获取API密钥？
   **A**: 用户注册 → 创建应用 → 获取密钥

2. **Q**: 支持哪些认证方式？
   **A**: 请求头、请求体、嵌套三种方式

3. **Q**: 如何处理余额不足？
   **A**: 监听402错误码，引导用户充值

4. **Q**: 配置管理需要什么权限？
   **A**: 需要管理员token，联系系统管理员

### 联系方式
- 📧 技术支持: <EMAIL>
- 📱 客服电话: 400-123-4567
- 💬 在线文档: 查看各模块详细文档
- 🔧 测试工具: 使用在线测试页面

## 🎉 开始开发

1. **选择适合的文档**: 根据需求选择对应的详细文档
2. **配置开发环境**: 设置API基础URL和认证信息
3. **集成核心功能**: 从拍照搜题API开始
4. **添加配置管理**: 如需管理功能，集成配置API
5. **测试和调试**: 使用提供的测试工具验证功能
6. **部署上线**: 切换到生产环境配置

祝您开发顺利！🚀
