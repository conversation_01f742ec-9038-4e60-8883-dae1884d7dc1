# 前端API接入完整指南

## 📋 概述

本文档为前端开发团队提供拍照搜题API和配置管理API的完整接入指南，包括用户认证、应用管理、拍照搜题、配置管理等所有功能模块。

## 🔗 基础信息

- **服务地址**: `http://localhost:8080`
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: API密钥认证

## 🏗️ 系统架构

```
前端应用
    ↓
用户认证 → 应用管理 → 拍照搜题
    ↓           ↓         ↓
配置管理 ← 系统管理 ← 统计分析
```

## 📱 核心功能模块

### 1. 用户认证模块

#### 用户注册
```javascript
// 发送验证码
const sendCode = async (phone) => {
  const response = await fetch('/api/v1/user/send-code', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ phone })
  });
  return response.json();
};

// 用户注册
const register = async (phone, password, code, inviteCode = 'SOLVE2024') => {
  const response = await fetch('/api/v1/user/register', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      phone,
      password,
      code,
      invite_code: inviteCode
    })
  });
  return response.json();
};
```

#### 用户登录
```javascript
const login = async (phone, password) => {
  const response = await fetch('/api/v1/user/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ phone, password })
  });
  return response.json();
};
```

#### 密码管理
```javascript
// 修改密码
const changePassword = async (userId, oldPassword, newPassword) => {
  const response = await fetch(`/api/v1/user/${userId}/change-password`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      old_password: oldPassword,
      new_password: newPassword
    })
  });
  return response.json();
};

// 忘记密码
const forgotPassword = async (phone) => {
  const response = await fetch('/api/v1/user/forgot-password', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ phone })
  });
  return response.json();
};

// 重置密码
const resetPassword = async (phone, code, newPassword) => {
  const response = await fetch('/api/v1/user/reset-password', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      phone,
      code,
      new_password: newPassword
    })
  });
  return response.json();
};
```

### 2. 应用管理模块

#### 创建应用
```javascript
const createApp = async (userId, name, type = 1) => {
  const response = await fetch(`/api/v1/user/${userId}/app`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ name, type })
  });
  return response.json();
};
```

#### 获取应用列表
```javascript
const getAppList = async (userId) => {
  const response = await fetch(`/api/v1/user/${userId}/app`);
  return response.json();
};
```

#### 获取应用详情（含密钥）
```javascript
const getAppDetail = async (userId, appId) => {
  const response = await fetch(`/api/v1/user/${userId}/app/${appId}`);
  return response.json();
};
```

#### 更新应用
```javascript
const updateApp = async (userId, appId, name) => {
  const response = await fetch(`/api/v1/user/${userId}/app/${appId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ name })
  });
  return response.json();
};
```

#### 重置应用密钥
```javascript
const resetAppSecret = async (userId, appId) => {
  const response = await fetch(`/api/v1/user/${userId}/app/${appId}/reset-secret`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' }
  });
  return response.json();
};
```

### 3. 拍照搜题模块

#### 多种认证方式支持

##### 方式1: 请求头认证（推荐）
```javascript
const searchWithHeaders = async (imageUrl, appKey, secretKey) => {
  const response = await fetch('/api/v1/api/search', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-App-Key': appKey,
      'X-Secret-Key': secretKey
    },
    body: JSON.stringify({ image_url: imageUrl })
  });
  return response.json();
};
```

##### 方式2: 请求体认证（简化集成）
```javascript
const searchWithBody = async (imageUrl, appKey, secretKey) => {
  const response = await fetch('/api/v1/api/search', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      app_key: appKey,
      secret_key: secretKey,
      image_url: imageUrl
    })
  });
  return response.json();
};
```

##### 方式3: 嵌套认证（结构化）
```javascript
const searchWithAuth = async (imageUrl, appKey, secretKey) => {
  const response = await fetch('/api/v1/api/search', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      auth: {
        app_key: appKey,
        secret_key: secretKey
      },
      image_url: imageUrl
    })
  });
  return response.json();
};
```

#### 通用搜题函数
```javascript
const searchQuestion = async (imageUrl, appKey, secretKey, authMethod = 'header') => {
  const methods = {
    header: searchWithHeaders,
    body: searchWithBody,
    auth: searchWithAuth
  };
  
  const searchFunc = methods[authMethod] || methods.header;
  return await searchFunc(imageUrl, appKey, secretKey);
};
```

### 4. 余额管理模块

#### 获取用户余额
```javascript
const getUserBalance = async (userId) => {
  const response = await fetch(`/api/v1/user/${userId}/balance`);
  return response.json();
};
```

#### 用户充值
```javascript
const recharge = async (userId, amount, description) => {
  const response = await fetch(`/api/v1/user/${userId}/recharge`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ amount, description })
  });
  return response.json();
};
```

#### 获取余额记录
```javascript
const getBalanceLogs = async (userId, page = 1, pageSize = 20, type = null) => {
  let url = `/api/v1/user/${userId}/balance/logs?page=${page}&page_size=${pageSize}`;
  if (type) url += `&type=${type}`;
  
  const response = await fetch(url);
  return response.json();
};
```

## 🔧 配置管理模块

### 1. 模型配置管理

#### 获取模型配置列表
```javascript
const getModelConfigs = async (page = 1, pageSize = 10) => {
  const response = await fetch(`/api/v1/admin/model?page=${page}&page_size=${pageSize}`, {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

#### 获取启用的模型配置
```javascript
const getEnabledModels = async () => {
  const response = await fetch('/api/v1/admin/model/enabled', {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

#### 创建模型配置
```javascript
const createModelConfig = async (modelData) => {
  const response = await fetch('/api/v1/admin/model', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer admin_token'
    },
    body: JSON.stringify({
      name: modelData.name,
      api_url: modelData.apiUrl,
      api_key: modelData.apiKey,
      params: JSON.stringify(modelData.params),
      status: modelData.status || 1
    })
  });
  return response.json();
};
```

#### 更新模型配置
```javascript
const updateModelConfig = async (id, modelData) => {
  const response = await fetch(`/api/v1/admin/model/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer admin_token'
    },
    body: JSON.stringify(modelData)
  });
  return response.json();
};
```

#### 更新模型状态
```javascript
const updateModelStatus = async (id, status) => {
  const response = await fetch(`/api/v1/admin/model/${id}/status`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer admin_token'
    },
    body: JSON.stringify({ status })
  });
  return response.json();
};
```

### 2. 价格配置管理

#### 获取价格配置列表
```javascript
const getPriceConfigs = async (page = 1, pageSize = 10) => {
  const response = await fetch(`/api/v1/admin/price?page=${page}&page_size=${pageSize}`, {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

#### 创建价格配置
```javascript
const createPriceConfig = async (priceData) => {
  const response = await fetch('/api/v1/admin/price', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer admin_token'
    },
    body: JSON.stringify({
      service_type: priceData.serviceType,
      price: priceData.price,
      description: priceData.description
    })
  });
  return response.json();
};
```

#### 根据服务类型获取价格
```javascript
const getPriceByService = async (serviceType, userId = null) => {
  let url = `/api/v1/admin/price/service/${serviceType}`;
  if (userId) url += `?user_id=${userId}`;

  const response = await fetch(url, {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

#### 设置用户专属价格
```javascript
const setUserPrice = async (serviceType, userId, price) => {
  const response = await fetch(`/api/v1/admin/price/service/${serviceType}/user/${userId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer admin_token'
    },
    body: JSON.stringify({ price })
  });
  return response.json();
};
```

### 3. 系统配置管理

#### 获取系统信息
```javascript
const getSystemInfo = async () => {
  const response = await fetch('/api/v1/admin/system/info', {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

#### 获取系统健康状态
```javascript
const getSystemHealth = async () => {
  const response = await fetch('/api/v1/admin/system/health', {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

#### 获取仪表板数据
```javascript
const getDashboard = async () => {
  const response = await fetch('/api/v1/admin/system/dashboard', {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

#### 更新系统配置
```javascript
const updateSystemConfig = async (configs) => {
  const response = await fetch('/api/v1/admin/system/config', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer admin_token'
    },
    body: JSON.stringify(configs)
  });
  return response.json();
};
```

#### 系统清理
```javascript
const cleanupSystem = async (options) => {
  const response = await fetch('/api/v1/admin/system/cleanup', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer admin_token'
    },
    body: JSON.stringify({
      clean_logs: options.cleanLogs || false,
      clean_stats: options.cleanStats || false,
      days: options.days || 30
    })
  });
  return response.json();
};
```

## 📊 统计分析模块

### 获取系统统计
```javascript
const getSystemStats = async (date = null) => {
  let url = '/api/v1/admin/stats/system';
  if (date) url += `?date=${date}`;

  const response = await fetch(url, {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

### 获取用户统计
```javascript
const getUserStats = async (userId, date = null) => {
  let url = `/api/v1/admin/stats/user/${userId}`;
  if (date) url += `?date=${date}`;

  const response = await fetch(url, {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

### 获取排行榜
```javascript
const getTopUsers = async (date = null, limit = 10) => {
  let url = `/api/v1/admin/stats/top-users?limit=${limit}`;
  if (date) url += `&date=${date}`;

  const response = await fetch(url, {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

## 🎨 React 组件示例

### 拍照搜题组件
```jsx
import React, { useState } from 'react';

const PhotoSearch = ({ appKey, secretKey }) => {
  const [imageUrl, setImageUrl] = useState('');
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [authMethod, setAuthMethod] = useState('header');

  const handleSearch = async () => {
    if (!imageUrl) return;

    setLoading(true);
    try {
      const data = await searchQuestion(imageUrl, appKey, secretKey, authMethod);
      if (data.code === 200) {
        setResult(data.data);
      } else {
        alert(data.message);
      }
    } catch (error) {
      alert('搜题失败：' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="photo-search">
      <div className="form-group">
        <label>认证方式：</label>
        <select value={authMethod} onChange={(e) => setAuthMethod(e.target.value)}>
          <option value="header">请求头认证</option>
          <option value="body">请求体认证</option>
          <option value="auth">嵌套认证</option>
        </select>
      </div>

      <div className="form-group">
        <label>图片URL：</label>
        <input
          type="url"
          value={imageUrl}
          onChange={(e) => setImageUrl(e.target.value)}
          placeholder="请输入图片URL"
        />
      </div>

      <button onClick={handleSearch} disabled={loading}>
        {loading ? '搜题中...' : '开始搜题'}
      </button>

      {result && (
        <div className="result">
          <h3>搜题结果</h3>
          <p><strong>题目类型：</strong>{result.question_type}</p>
          <p><strong>题目内容：</strong>{result.question_text}</p>
          {result.options && Object.keys(result.options).length > 0 && (
            <div>
              <strong>选项：</strong>
              {Object.entries(result.options).map(([key, value]) => (
                <div key={key}>{key}. {value}</div>
              ))}
            </div>
          )}
          <p><strong>答案：</strong>{result.answer}</p>
          <p><strong>解析：</strong>{result.analysis}</p>
        </div>
      )}
    </div>
  );
};

export default PhotoSearch;
```

### 模型配置管理组件
```jsx
import React, { useState, useEffect } from 'react';

const ModelConfigManager = () => {
  const [models, setModels] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadModels();
  }, []);

  const loadModels = async () => {
    setLoading(true);
    try {
      const data = await getModelConfigs();
      if (data.code === 200) {
        setModels(data.data.list);
      }
    } catch (error) {
      console.error('加载模型配置失败：', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleModelStatus = async (id, currentStatus) => {
    try {
      const newStatus = currentStatus === 1 ? 2 : 1;
      const data = await updateModelStatus(id, newStatus);
      if (data.code === 200) {
        loadModels(); // 重新加载列表
      }
    } catch (error) {
      alert('更新状态失败：' + error.message);
    }
  };

  return (
    <div className="model-config-manager">
      <h2>模型配置管理</h2>

      {loading ? (
        <div>加载中...</div>
      ) : (
        <table>
          <thead>
            <tr>
              <th>模型名称</th>
              <th>API地址</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {models.map(model => (
              <tr key={model.id}>
                <td>{model.name}</td>
                <td>{model.api_url}</td>
                <td>
                  <span className={model.status === 1 ? 'status-enabled' : 'status-disabled'}>
                    {model.status === 1 ? '启用' : '禁用'}
                  </span>
                </td>
                <td>
                  <button onClick={() => toggleModelStatus(model.id, model.status)}>
                    {model.status === 1 ? '禁用' : '启用'}
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default ModelConfigManager;
```
```
