# 拍照搜题API接口总览

## 📚 文档导航

| 文档 | 用途 | 适用人群 |
|------|------|----------|
| [PHOTO_SEARCH_API_GUIDE.md](./PHOTO_SEARCH_API_GUIDE.md) | 完整的API接口文档 | 前端开发工程师 |
| [QUICK_START_GUIDE.md](./QUICK_START_GUIDE.md) | 5分钟快速接入指南 | 快速上手的开发者 |
| [api_test_page.html](./api_test_page.html) | 在线API测试页面 | 测试和演示 |
| [ADMIN_USER_AUTH_API.md](./ADMIN_USER_AUTH_API.md) | 用户认证和应用管理 | 全栈开发工程师 |

## 🔗 核心接口

### 拍照搜题API
```
POST /api/v1/api/search
```
**功能**: 上传图片进行题目识别和解析  
**认证**: 需要 App Key 和 Secret Key  
**费用**: 0.1元/次  

### 用户管理API
```
POST /api/v1/user/register    # 用户注册
POST /api/v1/user/login       # 用户登录
GET  /api/v1/user/{id}/balance # 查询余额
```

### 应用管理API
```
POST /api/v1/user/{user_id}/app           # 创建应用
GET  /api/v1/user/{user_id}/app           # 获取应用列表
GET  /api/v1/user/{user_id}/app/{app_id}  # 获取应用详情(含密钥)
```

## 🚀 快速开始

### 1. 获取API密钥
```bash
# 注册用户 → 登录 → 创建应用 → 获取密钥
curl -X POST http://localhost:8080/api/v1/user/register \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000","password":"password123","code":"123456","invite_code":"SOLVE2024"}'
```

### 2. 调用搜题API
```javascript
const response = await fetch('/api/v1/api/search', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-App-Key': 'your_app_key',
    'X-Secret-Key': 'your_secret_key'
  },
  body: JSON.stringify({
    image_url: 'https://example.com/question.jpg'
  })
});
```

### 3. 处理响应
```javascript
const result = await response.json();
if (result.code === 200) {
  console.log('题目类型:', result.data.question_type);
  console.log('题目内容:', result.data.question_text);
  console.log('答案:', result.data.answer);
  console.log('解析:', result.data.analysis);
}
```

## 📊 响应数据结构

### 成功响应
```json
{
  "code": 200,
  "message": "搜题成功",
  "data": {
    "id": 1,
    "question_type": "单选题",
    "question_text": "题目内容",
    "options": {"A": "选项A", "B": "选项B"},
    "answer": "A",
    "analysis": "详细解析",
    "subject": "数学",
    "grade": "高中",
    "difficulty": 3,
    "cache_hit": false,
    "process_time": 1500
  }
}
```

### 支持的题目类型
- **单选题**: 有选项A、B、C、D
- **多选题**: 有多个正确选项
- **填空题**: 需要填入答案
- **解答题**: 需要详细解答过程
- **判断题**: 对错判断

## ⚠️ 错误处理

| 错误码 | 含义 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查图片URL格式 |
| 401 | API密钥无效 | 检查密钥配置 |
| 402 | 余额不足 | 用户充值 |
| 403 | 应用被冻结 | 联系管理员 |
| 429 | 请求过频 | 降低请求频率 |
| 500 | 服务器错误 | 稍后重试 |

## 🔧 开发工具

### 在线测试
打开 `api_test_page.html` 在浏览器中测试API

### 命令行测试
```bash
# 运行测试脚本
./test_refactored_api.sh
```

### 健康检查
```bash
curl http://localhost:8080/health
```

## 💰 计费说明

- **按次计费**: 每次成功识别收费0.1元
- **缓存优化**: 相同题目命中缓存不重复收费
- **余额查询**: 可通过API查询账户余额
- **充值方式**: 通过充值接口增加余额

## 🔒 安全建议

1. **密钥保护**: 不要在前端硬编码API密钥
2. **HTTPS**: 生产环境使用HTTPS协议
3. **频率限制**: 实现客户端请求限流
4. **图片安全**: 确保图片URL可公开访问

## 📱 SDK支持

### JavaScript/TypeScript
```javascript
class PhotoSearchAPI {
  constructor(appKey, secretKey) {
    this.appKey = appKey;
    this.secretKey = secretKey;
    this.baseURL = 'http://localhost:8080';
  }

  async search(imageUrl) {
    const response = await fetch(`${this.baseURL}/api/v1/api/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-App-Key': this.appKey,
        'X-Secret-Key': this.secretKey
      },
      body: JSON.stringify({ image_url: imageUrl })
    });
    
    return response.json();
  }
}
```

### Python
```python
import requests

class PhotoSearchAPI:
    def __init__(self, app_key, secret_key):
        self.app_key = app_key
        self.secret_key = secret_key
        self.base_url = 'http://localhost:8080'
    
    def search(self, image_url):
        response = requests.post(
            f'{self.base_url}/api/v1/api/search',
            headers={
                'Content-Type': 'application/json',
                'X-App-Key': self.app_key,
                'X-Secret-Key': self.secret_key
            },
            json={'image_url': image_url}
        )
        return response.json()
```

## 📞 技术支持

### 常见问题
1. **Q**: 如何获取API密钥？  
   **A**: 注册用户 → 创建应用 → 获取密钥

2. **Q**: 支持哪些图片格式？  
   **A**: jpg、png、gif、webp、bmp

3. **Q**: 如何提高识别准确率？  
   **A**: 使用清晰、完整的题目图片

4. **Q**: 缓存机制如何工作？  
   **A**: 相同题目内容会命中缓存，响应更快且不重复收费

### 联系方式
- 📧 技术支持: <EMAIL>
- 📱 客服电话: 400-123-4567
- 💬 在线客服: 工作日 9:00-18:00

## 🔄 版本更新

### v1.1.0 (最新)
- ✅ 支持多种题目类型识别
- ✅ 优化AI模型参数
- ✅ 改进缓存机制
- ✅ 增强错误处理

### v1.0.0
- ✅ 基础拍照搜题功能
- ✅ 用户认证系统
- ✅ 应用管理功能

---

**开始您的拍照搜题API集成之旅！** 🚀

如有任何问题，请参考详细文档或联系技术支持团队。
