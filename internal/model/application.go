package model

import (
	"time"

	"gorm.io/gorm"
)

// Application 应用表
type Application struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	UserID    uint           `gorm:"index;not null" json:"user_id"`
	Name      string         `gorm:"size:50;not null" json:"name"`
	Type      int            `gorm:"not null;comment:'业务类型 1:拍照搜题'" json:"type"`
	AppKey    string         `gorm:"uniqueIndex;size:32;not null" json:"app_key"`
	SecretKey string         `gorm:"size:64;not null" json:"secret_key"`
	Status    int            `gorm:"default:1;comment:'1:正常 2:冻结'" json:"status"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (Application) TableName() string {
	return "applications"
}

// ApplicationType 应用类型常量
const (
	ApplicationTypePhotoSearch = 1 // 拍照搜题
)

// ApplicationStatus 应用状态常量
const (
	ApplicationStatusNormal = 1 // 正常
	ApplicationStatusFrozen = 2 // 冻结
)

// IsNormal 检查应用状态是否正常
func (a *Application) IsNormal() bool {
	return a.Status == ApplicationStatusNormal
}

// IsFrozen 检查应用是否被冻结
func (a *Application) IsFrozen() bool {
	return a.Status == ApplicationStatusFrozen
}

// ApplicationCreateRequest 创建应用请求
type ApplicationCreateRequest struct {
	Name string `json:"name" binding:"required,min=1,max=50" example:"我的搜题应用"`
	Type int    `json:"type" binding:"required,oneof=1" example:"1"`
}

// ApplicationUpdateRequest 更新应用请求
type ApplicationUpdateRequest struct {
	Name string `json:"name" binding:"required,min=1,max=50" example:"新的应用名称"`
}

// ApplicationStatusUpdateRequest 更新应用状态请求
type ApplicationStatusUpdateRequest struct {
	Status int `json:"status" binding:"required,oneof=1 2" example:"1"`
}

// ApplicationResponse 应用信息响应
type ApplicationResponse struct {
	ID        uint      `json:"id"`
	UserID    uint      `json:"user_id"`
	Name      string    `json:"name"`
	Type      int       `json:"type"`
	AppKey    string    `json:"app_key"`
	SecretKey string    `json:"secret_key"`
	Status    int       `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ApplicationListResponse 应用列表响应
type ApplicationListResponse struct {
	ID        uint      `json:"id"`
	Name      string    `json:"name"`
	Type      int       `json:"type"`
	AppKey    string    `json:"app_key"`
	Status    int       `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ToResponse 转换为详细响应格式
func (a *Application) ToResponse() *ApplicationResponse {
	return &ApplicationResponse{
		ID:        a.ID,
		UserID:    a.UserID,
		Name:      a.Name,
		Type:      a.Type,
		AppKey:    a.AppKey,
		SecretKey: a.SecretKey,
		Status:    a.Status,
		CreatedAt: a.CreatedAt,
		UpdatedAt: a.UpdatedAt,
	}
}

// ToListResponse 转换为列表响应格式（不包含SecretKey）
func (a *Application) ToListResponse() *ApplicationListResponse {
	return &ApplicationListResponse{
		ID:        a.ID,
		Name:      a.Name,
		Type:      a.Type,
		AppKey:    a.AppKey,
		Status:    a.Status,
		CreatedAt: a.CreatedAt,
		UpdatedAt: a.UpdatedAt,
	}
}

// GetTypeString 获取应用类型字符串
func (a *Application) GetTypeString() string {
	switch a.Type {
	case ApplicationTypePhotoSearch:
		return "拍照搜题"
	default:
		return "未知类型"
	}
}

// GetStatusString 获取应用状态字符串
func (a *Application) GetStatusString() string {
	switch a.Status {
	case ApplicationStatusNormal:
		return "正常"
	case ApplicationStatusFrozen:
		return "冻结"
	default:
		return "未知状态"
	}
}
