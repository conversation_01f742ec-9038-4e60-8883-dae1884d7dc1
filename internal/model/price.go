package model

import (
	"time"

	"gorm.io/gorm"
)

// PriceConfig 价格配置表
type PriceConfig struct {
	ID          uint           `gorm:"primaryKey" json:"id"`
	ServiceType int            `gorm:"not null;comment:'服务类型 1:拍照搜题'" json:"service_type"`
	UserID      uint           `gorm:"default:0;comment:'0表示系统默认价格'" json:"user_id"`
	Price       float64        `gorm:"not null;comment:'单次调用价格'" json:"price"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (PriceConfig) TableName() string {
	return "price_configs"
}

// BalanceLog 余额变动日志表
type BalanceLog struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	UserID      uint      `gorm:"index;not null" json:"user_id"`
	Amount      float64   `gorm:"not null;comment:'变动金额'" json:"amount"`
	Balance     float64   `gorm:"not null;comment:'变动后余额'" json:"balance"`
	Type        int       `gorm:"not null;comment:'类型 1:充值 2:消费 3:退款'" json:"type"`
	Description string    `gorm:"size:255" json:"description"`
	RelatedID   uint      `gorm:"comment:'关联ID'" json:"related_id"`
	CreatedAt   time.Time `json:"created_at"`
}

// TableName 指定表名
func (BalanceLog) TableName() string {
	return "balance_logs"
}

// BalanceLogType 余额变动类型常量
const (
	BalanceLogTypeRecharge = 1 // 充值
	BalanceLogTypeConsume  = 2 // 消费
	BalanceLogTypeRefund   = 3 // 退款
)

// PriceConfigCreateRequest 创建价格配置请求
type PriceConfigCreateRequest struct {
	ServiceType int     `json:"service_type" binding:"required,oneof=1" example:"1"`
	UserID      uint    `json:"user_id" example:"0"`
	Price       float64 `json:"price" binding:"required,min=0" example:"0.01"`
}

// PriceConfigUpdateRequest 更新价格配置请求
type PriceConfigUpdateRequest struct {
	Price float64 `json:"price" binding:"required,min=0" example:"0.01"`
}

// UserRechargeRequest 用户充值请求
type UserRechargeRequest struct {
	Amount      float64 `json:"amount" binding:"required,min=0.01" example:"10.00"`
	Description string  `json:"description" example:"账户充值"`
}

// PriceConfigResponse 价格配置响应
type PriceConfigResponse struct {
	ID          uint      `json:"id"`
	ServiceType int       `json:"service_type"`
	UserID      uint      `json:"user_id"`
	Price       float64   `json:"price"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (p *PriceConfig) ToResponse() *PriceConfigResponse {
	return &PriceConfigResponse{
		ID:          p.ID,
		ServiceType: p.ServiceType,
		UserID:      p.UserID,
		Price:       p.Price,
		CreatedAt:   p.CreatedAt,
		UpdatedAt:   p.UpdatedAt,
	}
}

// BalanceLogResponse 余额日志响应
type BalanceLogResponse struct {
	ID          uint      `json:"id"`
	UserID      uint      `json:"user_id"`
	Amount      float64   `json:"amount"`
	Balance     float64   `json:"balance"`
	Type        int       `json:"type"`
	TypeName    string    `json:"type_name"`
	Description string    `json:"description"`
	RelatedID   uint      `json:"related_id"`
	CreatedAt   time.Time `json:"created_at"`
}

// ToResponse 转换为响应格式
func (b *BalanceLog) ToResponse() *BalanceLogResponse {
	typeName := ""
	switch b.Type {
	case BalanceLogTypeRecharge:
		typeName = "充值"
	case BalanceLogTypeConsume:
		typeName = "消费"
	case BalanceLogTypeRefund:
		typeName = "退款"
	}

	return &BalanceLogResponse{
		ID:          b.ID,
		UserID:      b.UserID,
		Amount:      b.Amount,
		Balance:     b.Balance,
		Type:        b.Type,
		TypeName:    typeName,
		Description: b.Description,
		RelatedID:   b.RelatedID,
		CreatedAt:   b.CreatedAt,
	}
}

// GetServiceTypeName 获取服务类型名称
func GetServiceTypeName(serviceType int) string {
	switch serviceType {
	case ApplicationTypePhotoSearch:
		return "拍照搜题"
	default:
		return "未知服务"
	}
}
