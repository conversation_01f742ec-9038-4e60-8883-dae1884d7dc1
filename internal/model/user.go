package model

import (
	"time"

	"gorm.io/gorm"
)

// User 用户表
type User struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	Phone     string         `gorm:"uniqueIndex;size:20;not null" json:"phone"`
	Password  string         `gorm:"size:100;not null" json:"-"` // 存储加密后的密码，不返回给前端
	Balance   float64        `gorm:"default:0" json:"balance"`
	Status    int            `gorm:"default:1" json:"status"` // 1:正常 2:冻结
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// UserStatus 用户状态常量
const (
	UserStatusNormal = 1 // 正常
	UserStatusFrozen = 2 // 冻结
)

// IsNormal 检查用户状态是否正常
func (u *User) IsNormal() bool {
	return u.Status == UserStatusNormal
}

// IsFrozen 检查用户是否被冻结
func (u *User) IsFrozen() bool {
	return u.Status == UserStatusFrozen
}

// UserRegisterRequest 用户注册请求
type UserRegisterRequest struct {
	Phone      string `json:"phone" binding:"required,len=11" example:"13800138000"`
	Password   string `json:"password" binding:"required,min=6,max=20" example:"123456"`
	Code       string `json:"code" binding:"required,len=6" example:"123456"`
	InviteCode string `json:"invite_code" binding:"required" example:"SOLVE2024"`
}

// UserLoginRequest 用户登录请求
type UserLoginRequest struct {
	Phone    string `json:"phone" binding:"required,len=11" example:"13800138000"`
	Password string `json:"password" binding:"required" example:"123456"`
}

// UserProfileUpdateRequest 用户信息更新请求
type UserProfileUpdateRequest struct {
	Password string `json:"password,omitempty" binding:"omitempty,min=6,max=20" example:"newpassword"`
}

// UserChangePasswordRequest 用户修改密码请求（使用原密码）
type UserChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required" example:"oldpassword123"`
	NewPassword string `json:"new_password" binding:"required,min=6,max=20" example:"newpassword123"`
}

// UserResetPasswordRequest 用户重置密码请求（使用验证码）
type UserResetPasswordRequest struct {
	Phone       string `json:"phone" binding:"required,len=11" example:"13800138000"`
	Code        string `json:"code" binding:"required,len=6" example:"123456"`
	NewPassword string `json:"new_password" binding:"required,min=6,max=20" example:"newpassword123"`
}

// UserForgotPasswordRequest 用户忘记密码请求（发送验证码）
type UserForgotPasswordRequest struct {
	Phone string `json:"phone" binding:"required,len=11" example:"13800138000"`
}

// SendCodeRequest 发送验证码请求
type SendCodeRequest struct {
	Phone string `json:"phone" binding:"required,len=11" example:"13800138000"`
}

// UserResponse 用户信息响应
type UserResponse struct {
	ID        uint      `json:"id"`
	Phone     string    `json:"phone"`
	Balance   float64   `json:"balance"`
	Status    int       `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (u *User) ToResponse() *UserResponse {
	return &UserResponse{
		ID:        u.ID,
		Phone:     u.Phone,
		Balance:   u.Balance,
		Status:    u.Status,
		CreatedAt: u.CreatedAt,
		UpdatedAt: u.UpdatedAt,
	}
}
