package middleware

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"solve_api/internal/database"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"solve_api/internal/utils"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// AuthCredentials 认证凭据结构
type AuthCredentials struct {
	AppKey    string `json:"app_key"`
	SecretKey string `json:"secret_key"`
	Timestamp int64  `json:"timestamp,omitempty"`
	Signature string `json:"signature,omitempty"`
}

// APIAuth API密钥认证中间件（支持多种认证方式）
func APIAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过健康检查和管理员接口
		if strings.HasPrefix(c.Request.URL.Path, "/health") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/admin") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/user") {
			c.Next()
			return
		}

		// 尝试多种认证方式
		var appKey, secretKey string
		var authMethod string

		// 方式1: 请求头认证（推荐）
		headerAppKey := c.GetHeader("X-App-Key")
		headerSecretKey := c.GetHeader("X-Secret-Key")

		if headerAppKey != "" && headerSecretKey != "" {
			appKey = headerAppKey
			secretKey = headerSecretKey
			authMethod = "header"
		} else {
			// 方式2: 请求体认证（备选）
			bodyAppKey, bodySecretKey, err := extractAuthFromBody(c)
			if err != nil {
				utils.BadRequest(c, "认证信息解析失败: "+err.Error())
				c.Abort()
				return
			}

			if bodyAppKey != "" && bodySecretKey != "" {
				appKey = bodyAppKey
				secretKey = bodySecretKey
				authMethod = "body"
			}
		}

		// 检查是否获取到认证信息
		if appKey == "" || secretKey == "" {
			utils.Unauthorized(c, "缺少API密钥，请在请求头或请求体中提供app_key和secret_key")
			c.Abort()
			return
		}

		// 验证应用密钥
		app, user, err := validateCredentials(appKey, secretKey)
		if err != nil {
			utils.Unauthorized(c, err.Error())
			c.Abort()
			return
		}

		// 将认证信息存储到上下文中
		c.Set("app", app)
		c.Set("user", user)
		c.Set("app_id", app.ID)
		c.Set("user_id", user.ID)
		c.Set("app_type", app.Type)
		c.Set("auth_method", authMethod)

		c.Next()
	}
}

// extractAuthFromBody 从请求体中提取认证信息
func extractAuthFromBody(c *gin.Context) (string, string, error) {
	// 读取请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return "", "", fmt.Errorf("读取请求体失败")
	}

	// 重新设置请求体，以便后续处理
	c.Request.Body = io.NopCloser(strings.NewReader(string(body)))

	// 解析JSON
	var requestData map[string]interface{}
	if err := json.Unmarshal(body, &requestData); err != nil {
		return "", "", fmt.Errorf("JSON格式错误")
	}

	// 方式2a: 直接在根级别
	if appKey, ok := requestData["app_key"].(string); ok {
		if secretKey, ok := requestData["secret_key"].(string); ok {
			return appKey, secretKey, nil
		}
	}

	// 方式2b: 在auth对象中
	if authObj, ok := requestData["auth"].(map[string]interface{}); ok {
		if appKey, ok := authObj["app_key"].(string); ok {
			if secretKey, ok := authObj["secret_key"].(string); ok {
				return appKey, secretKey, nil
			}
		}
	}

	return "", "", nil
}

// validateCredentials 验证认证凭据
func validateCredentials(appKey, secretKey string) (*model.Application, *model.User, error) {
	// 验证应用密钥
	appRepo := repository.NewApplicationRepository(database.GetDB())
	app, err := appRepo.GetByAppKey(appKey)
	if err != nil {
		return nil, nil, fmt.Errorf("验证API密钥失败")
	}

	if app == nil {
		return nil, nil, fmt.Errorf("无效的AppKey")
	}

	// 验证SecretKey
	if app.SecretKey != secretKey {
		return nil, nil, fmt.Errorf("无效的SecretKey")
	}

	// 检查应用状态
	if app.IsFrozen() {
		return nil, nil, fmt.Errorf("应用已被冻结")
	}

	// 检查用户状态
	userRepo := repository.NewUserRepository(database.GetDB())
	user, err := userRepo.GetByID(app.UserID)
	if err != nil {
		return nil, nil, fmt.Errorf("查询用户信息失败")
	}

	if user == nil {
		return nil, nil, fmt.Errorf("用户不存在")
	}

	if user.IsFrozen() {
		return nil, nil, fmt.Errorf("用户账户已被冻结")
	}

	return app, user, nil
}

// SignatureAuth 签名认证中间件（可选的高安全性认证）
func SignatureAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过健康检查和管理员接口
		if strings.HasPrefix(c.Request.URL.Path, "/health") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/admin") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/user") {
			c.Next()
			return
		}

		// 从请求体中提取签名认证信息
		appKey, timestamp, signature, err := extractSignatureAuth(c)
		if err != nil {
			utils.BadRequest(c, "签名认证信息解析失败: "+err.Error())
			c.Abort()
			return
		}

		if appKey == "" || signature == "" {
			// 如果没有签名信息，回退到普通认证
			c.Next()
			return
		}

		// 验证时间戳（防重放攻击）
		if !validateTimestamp(timestamp) {
			utils.Unauthorized(c, "请求时间戳无效或已过期")
			c.Abort()
			return
		}

		// 获取应用信息
		appRepo := repository.NewApplicationRepository(database.GetDB())
		app, err := appRepo.GetByAppKey(appKey)
		if err != nil || app == nil {
			utils.Unauthorized(c, "无效的AppKey")
			c.Abort()
			return
		}

		// 验证签名
		if !validateSignature(c, app.SecretKey, appKey, timestamp, signature) {
			utils.Unauthorized(c, "签名验证失败")
			c.Abort()
			return
		}

		// 检查应用和用户状态
		userRepo := repository.NewUserRepository(database.GetDB())
		user, err := userRepo.GetByID(app.UserID)
		if err != nil || user == nil {
			utils.Unauthorized(c, "用户不存在")
			c.Abort()
			return
		}

		if app.IsFrozen() || user.IsFrozen() {
			utils.Forbidden(c, "应用或用户已被冻结")
			c.Abort()
			return
		}

		// 将认证信息存储到上下文中
		c.Set("app", app)
		c.Set("user", user)
		c.Set("app_id", app.ID)
		c.Set("user_id", user.ID)
		c.Set("app_type", app.Type)
		c.Set("auth_method", "signature")

		c.Next()
	}
}

// extractSignatureAuth 从请求体中提取签名认证信息
func extractSignatureAuth(c *gin.Context) (string, int64, string, error) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return "", 0, "", fmt.Errorf("读取请求体失败")
	}

	// 重新设置请求体
	c.Request.Body = io.NopCloser(strings.NewReader(string(body)))

	var requestData map[string]interface{}
	if err := json.Unmarshal(body, &requestData); err != nil {
		return "", 0, "", fmt.Errorf("JSON格式错误")
	}

	appKey, _ := requestData["app_key"].(string)
	signature, _ := requestData["signature"].(string)

	var timestamp int64
	if ts, ok := requestData["timestamp"].(float64); ok {
		timestamp = int64(ts)
	} else if ts, ok := requestData["timestamp"].(string); ok {
		timestamp, _ = strconv.ParseInt(ts, 10, 64)
	}

	return appKey, timestamp, signature, nil
}

// validateTimestamp 验证时间戳（5分钟有效期）
func validateTimestamp(timestamp int64) bool {
	if timestamp == 0 {
		return false
	}

	now := time.Now().Unix()
	diff := now - timestamp

	// 允许5分钟的时间差
	return diff >= 0 && diff <= 300
}

// validateSignature 验证签名
func validateSignature(c *gin.Context, secretKey, appKey string, timestamp int64, signature string) bool {
	// 读取请求体用于签名验证
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return false
	}

	// 重新设置请求体
	c.Request.Body = io.NopCloser(strings.NewReader(string(body)))

	// 构建签名字符串：method + url + timestamp + body
	signString := fmt.Sprintf("%s\n%s\n%d\n%s",
		c.Request.Method,
		c.Request.URL.Path,
		timestamp,
		string(body))

	// 使用HMAC-SHA256生成签名
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(signString))
	expectedSignature := hex.EncodeToString(h.Sum(nil))

	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// GenerateSignature 生成签名的辅助函数（供客户端使用）
func GenerateSignature(method, path string, timestamp int64, body, secretKey string) string {
	signString := fmt.Sprintf("%s\n%s\n%d\n%s", method, path, timestamp, body)
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(signString))
	return hex.EncodeToString(h.Sum(nil))
}

// GetAppFromContext 从上下文获取应用信息
func GetAppFromContext(c *gin.Context) (*model.Application, bool) {
	app, exists := c.Get("app")
	if !exists {
		return nil, false
	}
	return app.(*model.Application), true
}

// GetUserFromContext 从上下文获取用户信息
func GetUserFromContext(c *gin.Context) (*model.User, bool) {
	user, exists := c.Get("user")
	if !exists {
		return nil, false
	}
	return user.(*model.User), true
}

// GetAppIDFromContext 从上下文获取应用ID
func GetAppIDFromContext(c *gin.Context) (uint, bool) {
	appID, exists := c.Get("app_id")
	if !exists {
		return 0, false
	}
	return appID.(uint), true
}

// GetUserIDFromContext 从上下文获取用户ID
func GetUserIDFromContext(c *gin.Context) (uint, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return 0, false
	}
	return userID.(uint), true
}

// GetAppTypeFromContext 从上下文获取应用类型
func GetAppTypeFromContext(c *gin.Context) (int, bool) {
	appType, exists := c.Get("app_type")
	if !exists {
		return 0, false
	}
	return appType.(int), true
}
