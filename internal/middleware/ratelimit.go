package middleware

import (
	"context"
	"fmt"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/utils"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// RateLimit 限流中间件
func RateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过健康检查和管理员接口
		if strings.HasPrefix(c.Request.URL.Path, "/health") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/admin") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/user") {
			c.Next()
			return
		}

		// 如果Redis不可用，跳过限流
		rdb := database.GetRedis()
		if rdb == nil {
			c.Next()
			return
		}

		// 获取应用信息
		appKey := c.GetHeader("X-App-Key")
		if appKey == "" {
			c.Next()
			return
		}

		// 获取限流配置
		rateLimit := config.GlobalConfig.App.RateLimit
		if rateLimit <= 0 {
			rateLimit = 10 // 默认10次/秒
		}

		// 构造Redis键
		key := fmt.Sprintf("rate_limit:%s", appKey)
		ctx := context.Background()

		// 使用滑动窗口限流算法
		now := time.Now().Unix()
		window := int64(60) // 1分钟窗口
		maxRequests := int64(rateLimit * 60) // 每分钟最大请求数

		// 使用Redis的ZSET实现滑动窗口
		pipe := rdb.Pipeline()
		
		// 1. 移除过期的请求记录
		pipe.ZRemRangeByScore(ctx, key, "0", strconv.FormatInt(now-window, 10))
		
		// 2. 添加当前请求
		pipe.ZAdd(ctx, key, redis.Z{
			Score:  float64(now),
			Member: fmt.Sprintf("%d_%s", now, c.ClientIP()),
		})
		
		// 3. 获取当前窗口内的请求数
		pipe.ZCard(ctx, key)
		
		// 4. 设置过期时间
		pipe.Expire(ctx, key, time.Duration(window)*time.Second)

		results, err := pipe.Exec(ctx)
		if err != nil {
			// Redis错误时不阻止请求
			c.Next()
			return
		}

		// 获取当前请求数
		count := results[2].(*redis.IntCmd).Val()
		
		if count > maxRequests {
			utils.TooManyRequests(c, fmt.Sprintf("请求频率过高，每分钟最多%d次请求", maxRequests))
			c.Abort()
			return
		}

		// 设置响应头显示限流信息
		c.Header("X-RateLimit-Limit", strconv.FormatInt(maxRequests, 10))
		c.Header("X-RateLimit-Remaining", strconv.FormatInt(maxRequests-count, 10))
		c.Header("X-RateLimit-Reset", strconv.FormatInt(now+window, 10))

		c.Next()
	}
}

// RateLimitByIP IP限流中间件（用于用户接口）
func RateLimitByIP(maxRequests int, window time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 如果Redis不可用，跳过限流
		rdb := database.GetRedis()
		if rdb == nil {
			c.Next()
			return
		}

		// 获取客户端IP
		clientIP := c.ClientIP()
		
		// 构造Redis键
		key := fmt.Sprintf("ip_rate_limit:%s", clientIP)
		ctx := context.Background()

		// 使用简单计数器限流
		current, err := rdb.Incr(ctx, key).Result()
		if err != nil {
			// Redis错误时不阻止请求
			c.Next()
			return
		}

		// 第一次请求时设置过期时间
		if current == 1 {
			rdb.Expire(ctx, key, window)
		}

		if current > int64(maxRequests) {
			utils.TooManyRequests(c, fmt.Sprintf("IP请求频率过高，%v内最多%d次请求", window, maxRequests))
			c.Abort()
			return
		}

		// 设置响应头显示限流信息
		c.Header("X-RateLimit-Limit", strconv.Itoa(maxRequests))
		c.Header("X-RateLimit-Remaining", strconv.FormatInt(int64(maxRequests)-current, 10))

		c.Next()
	}
}

// GetRateLimitInfo 获取限流信息
func GetRateLimitInfo(appKey string) (int64, int64, error) {
	rdb := database.GetRedis()
	if rdb == nil {
		return 0, 0, fmt.Errorf("Redis不可用")
	}

	key := fmt.Sprintf("rate_limit:%s", appKey)
	ctx := context.Background()

	// 获取当前窗口内的请求数
	now := time.Now().Unix()
	window := int64(60)
	
	// 清理过期记录并获取当前计数
	pipe := rdb.Pipeline()
	pipe.ZRemRangeByScore(ctx, key, "0", strconv.FormatInt(now-window, 10))
	pipe.ZCard(ctx, key)
	
	results, err := pipe.Exec(ctx)
	if err != nil {
		return 0, 0, err
	}

	count := results[1].(*redis.IntCmd).Val()
	rateLimit := config.GlobalConfig.App.RateLimit
	maxRequests := int64(rateLimit * 60)

	return count, maxRequests - count, nil
}
