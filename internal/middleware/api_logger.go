package middleware

import (
	"bytes"
	"io"
	"solve_api/internal/database"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// APILogger API调用日志记录中间件
func APILogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过健康检查和管理员接口
		if strings.HasPrefix(c.Request.URL.Path, "/health") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/admin") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/user") {
			c.Next()
			return
		}

		// 记录开始时间
		startTime := time.Now()

		// 读取请求体大小
		var requestSize int64 = 0
		if c.Request.Body != nil {
			bodyBytes, _ := io.ReadAll(c.Request.Body)
			requestSize = int64(len(bodyBytes))
			// 重新设置请求体
			c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
		}

		// 创建响应写入器包装器
		responseWriter := &responseWriterWrapper{
			ResponseWriter: c.Writer,
			body:          &bytes.Buffer{},
		}
		c.Writer = responseWriter

		// 处理请求
		c.Next()

		// 计算响应时间
		responseTime := time.Since(startTime).Milliseconds()

		// 获取响应大小
		responseSize := int64(responseWriter.body.Len())

		// 获取用户和应用信息
		userID, _ := GetUserIDFromContext(c)
		appID, _ := GetAppIDFromContext(c)
		appType, _ := GetAppTypeFromContext(c)

		// 获取错误信息
		errorMsg := ""
		if len(c.Errors) > 0 {
			errorMsg = c.Errors.String()
		}

		// 获取服务费用
		servicePrice, _ := GetServicePriceFromContext(c)

		// 创建API日志
		apiLog := &model.APILog{
			UserID:       userID,
			AppID:        appID,
			Method:       c.Request.Method,
			Path:         c.Request.URL.Path,
			UserAgent:    c.Request.UserAgent(),
			ClientIP:     c.ClientIP(),
			StatusCode:   c.Writer.Status(),
			ResponseTime: responseTime,
			RequestSize:  requestSize,
			ResponseSize: responseSize,
			ErrorMsg:     errorMsg,
			ServiceType:  appType,
			Cost:         servicePrice,
		}

		// 异步保存日志
		go func() {
			db := database.GetDB()
			if db != nil {
				apiLogRepo := repository.NewAPILogRepository(db)
				apiLogRepo.Create(apiLog)
			}
		}()
	}
}

// responseWriterWrapper 响应写入器包装器
type responseWriterWrapper struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

// Write 写入响应数据
func (w *responseWriterWrapper) Write(data []byte) (int, error) {
	// 写入到缓冲区
	w.body.Write(data)
	// 写入到原始响应写入器
	return w.ResponseWriter.Write(data)
}

// WriteString 写入字符串响应数据
func (w *responseWriterWrapper) WriteString(s string) (int, error) {
	// 写入到缓冲区
	w.body.WriteString(s)
	// 写入到原始响应写入器
	return w.ResponseWriter.WriteString(s)
}

// GetBody 获取响应体内容
func (w *responseWriterWrapper) GetBody() []byte {
	return w.body.Bytes()
}
