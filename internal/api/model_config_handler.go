package api

import (
	"solve_api/internal/model"
	"solve_api/internal/service"
	"solve_api/internal/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ModelConfigHandler struct {
	modelConfigService *service.ModelConfigService
}

// NewModelConfigHandler 创建模型配置处理器实例
func NewModelConfigHandler(modelConfigService *service.ModelConfigService) *ModelConfigHandler {
	return &ModelConfigHandler{
		modelConfigService: modelConfigService,
	}
}

// Create 创建模型配置
// @Summary 创建模型配置
// @Description 创建新的AI模型配置
// @Tags 模型配置管理
// @Accept json
// @Produce json
// @Param request body model.ModelConfigCreateRequest true "创建模型配置请求参数"
// @Success 200 {object} utils.Response{data=model.ModelConfigResponse} "创建成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 409 {object} utils.Response "模型名称已存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/model [post]
func (h *ModelConfigHandler) Create(c *gin.Context) {
	var req model.ModelConfigCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	modelConfig, err := h.modelConfigService.Create(&req)
	if err != nil {
		if err.Error() == "模型名称已存在" {
			utils.Conflict(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "模型配置创建成功", modelConfig)
}

// GetByID 获取模型配置详情
// @Summary 获取模型配置详情
// @Description 根据ID获取模型配置的详细信息
// @Tags 模型配置管理
// @Accept json
// @Produce json
// @Param id path uint true "模型配置ID"
// @Success 200 {object} utils.Response{data=model.ModelConfigResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "模型配置不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/model/{id} [get]
func (h *ModelConfigHandler) GetByID(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		utils.BadRequest(c, "模型配置ID不能为空")
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "模型配置ID格式错误")
		return
	}

	modelConfig, err := h.modelConfigService.GetByID(uint(id))
	if err != nil {
		if err.Error() == "模型配置不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取模型配置成功", modelConfig)
}

// Update 更新模型配置
// @Summary 更新模型配置
// @Description 更新模型配置信息
// @Tags 模型配置管理
// @Accept json
// @Produce json
// @Param id path uint true "模型配置ID"
// @Param request body model.ModelConfigUpdateRequest true "更新模型配置请求参数"
// @Success 200 {object} utils.Response{data=model.ModelConfigResponse} "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "模型配置不存在"
// @Failure 409 {object} utils.Response "模型名称已存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/model/{id} [put]
func (h *ModelConfigHandler) Update(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		utils.BadRequest(c, "模型配置ID不能为空")
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "模型配置ID格式错误")
		return
	}

	var req model.ModelConfigUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	modelConfig, err := h.modelConfigService.Update(uint(id), &req)
	if err != nil {
		if err.Error() == "模型配置不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		if err.Error() == "模型名称已存在" {
			utils.Conflict(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "模型配置更新成功", modelConfig)
}

// Delete 删除模型配置
// @Summary 删除模型配置
// @Description 删除指定的模型配置
// @Tags 模型配置管理
// @Accept json
// @Produce json
// @Param id path uint true "模型配置ID"
// @Success 200 {object} utils.Response "删除成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "模型配置不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/model/{id} [delete]
func (h *ModelConfigHandler) Delete(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		utils.BadRequest(c, "模型配置ID不能为空")
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "模型配置ID格式错误")
		return
	}

	err = h.modelConfigService.Delete(uint(id))
	if err != nil {
		if err.Error() == "模型配置不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "模型配置删除成功", nil)
}

// GetList 获取模型配置列表
// @Summary 获取模型配置列表
// @Description 分页获取模型配置列表
// @Tags 模型配置管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} utils.Response{data=[]model.ModelConfigListResponse} "获取成功"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/model [get]
func (h *ModelConfigHandler) GetList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	modelConfigs, total, err := h.modelConfigService.GetList(page, pageSize)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	result := gin.H{
		"list":      modelConfigs,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	}

	utils.SuccessWithMessage(c, "获取模型配置列表成功", result)
}

// GetEnabled 获取启用的模型配置列表
// @Summary 获取启用的模型配置列表
// @Description 获取所有启用状态的模型配置
// @Tags 模型配置管理
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response{data=[]model.ModelConfigListResponse} "获取成功"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/model/enabled [get]
func (h *ModelConfigHandler) GetEnabled(c *gin.Context) {
	modelConfigs, err := h.modelConfigService.GetEnabled()
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取启用的模型配置成功", modelConfigs)
}

// UpdateStatus 更新模型状态
// @Summary 更新模型状态
// @Description 启用或禁用模型配置
// @Tags 模型配置管理
// @Accept json
// @Produce json
// @Param id path uint true "模型配置ID"
// @Param request body model.ModelConfigStatusUpdateRequest true "更新状态请求参数"
// @Success 200 {object} utils.Response{data=model.ModelConfigResponse} "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "模型配置不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/model/{id}/status [put]
func (h *ModelConfigHandler) UpdateStatus(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		utils.BadRequest(c, "模型配置ID不能为空")
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "模型配置ID格式错误")
		return
	}

	var req model.ModelConfigStatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	modelConfig, err := h.modelConfigService.UpdateStatus(uint(id), &req)
	if err != nil {
		if err.Error() == "模型配置不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "模型状态更新成功", modelConfig)
}
