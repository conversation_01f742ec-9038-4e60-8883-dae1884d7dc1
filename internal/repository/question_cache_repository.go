package repository

import (
	"context"
	"encoding/json"
	"solve_api/internal/config"
	"solve_api/internal/model"
	"time"

	"github.com/redis/go-redis/v9"
)

type QuestionCacheRepository struct {
	rdb *redis.Client
}

// NewQuestionCacheRepository 创建题目缓存仓库实例
func NewQuestionCacheRepository(rdb *redis.Client) *QuestionCacheRepository {
	return &QuestionCacheRepository{rdb: rdb}
}

// Set 设置题目缓存
func (r *QuestionCacheRepository) Set(hash string, question *model.Question) error {
	if r.rdb == nil {
		return nil // Redis不可用时不报错
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	// 序列化题目数据
	data, err := json.Marshal(question)
	if err != nil {
		return err
	}

	// 设置缓存，TTL为配置的缓存时间
	ttl := time.Duration(config.GlobalConfig.App.CacheTTL) * time.Second
	if ttl <= 0 {
		ttl = 7 * 24 * time.Hour // 默认7天
	}

	return r.rdb.Set(ctx, key, data, ttl).Err()
}

// Get 获取题目缓存
func (r *QuestionCacheRepository) Get(hash string) (*model.Question, error) {
	if r.rdb == nil {
		return nil, nil // Redis不可用时返回nil
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	// 获取缓存数据
	data, err := r.rdb.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存不存在
		}
		return nil, err
	}

	// 反序列化题目数据
	var question model.Question
	if err := json.Unmarshal([]byte(data), &question); err != nil {
		return nil, err
	}

	return &question, nil
}

// Delete 删除题目缓存
func (r *QuestionCacheRepository) Delete(hash string) error {
	if r.rdb == nil {
		return nil // Redis不可用时不报错
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	return r.rdb.Del(ctx, key).Err()
}

// Exists 检查缓存是否存在
func (r *QuestionCacheRepository) Exists(hash string) (bool, error) {
	if r.rdb == nil {
		return false, nil // Redis不可用时返回false
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	count, err := r.rdb.Exists(ctx, key).Result()
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// SetTTL 设置缓存过期时间
func (r *QuestionCacheRepository) SetTTL(hash string, ttl time.Duration) error {
	if r.rdb == nil {
		return nil // Redis不可用时不报错
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	return r.rdb.Expire(ctx, key, ttl).Err()
}

// GetTTL 获取缓存剩余时间
func (r *QuestionCacheRepository) GetTTL(hash string) (time.Duration, error) {
	if r.rdb == nil {
		return 0, nil // Redis不可用时返回0
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	return r.rdb.TTL(ctx, key).Result()
}

// GetKeys 获取所有题目缓存键
func (r *QuestionCacheRepository) GetKeys(pattern string) ([]string, error) {
	if r.rdb == nil {
		return nil, nil // Redis不可用时返回空
	}

	ctx := context.Background()
	if pattern == "" {
		pattern = "question:*"
	}

	return r.rdb.Keys(ctx, pattern).Result()
}

// Clear 清空所有题目缓存
func (r *QuestionCacheRepository) Clear() error {
	if r.rdb == nil {
		return nil // Redis不可用时不报错
	}

	ctx := context.Background()
	keys, err := r.GetKeys("question:*")
	if err != nil {
		return err
	}

	if len(keys) == 0 {
		return nil
	}

	return r.rdb.Del(ctx, keys...).Err()
}

// GetCacheStats 获取缓存统计信息
func (r *QuestionCacheRepository) GetCacheStats() (map[string]interface{}, error) {
	if r.rdb == nil {
		return map[string]interface{}{
			"redis_available": false,
			"total_keys":      0,
		}, nil
	}

	ctx := context.Background()
	
	// 获取所有题目缓存键
	keys, err := r.GetKeys("question:*")
	if err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"redis_available": true,
		"total_keys":      len(keys),
	}

	// 获取Redis内存使用情况
	info, err := r.rdb.Info(ctx, "memory").Result()
	if err == nil {
		stats["memory_info"] = info
	}

	return stats, nil
}

// BatchSet 批量设置缓存
func (r *QuestionCacheRepository) BatchSet(questions map[string]*model.Question) error {
	if r.rdb == nil {
		return nil // Redis不可用时不报错
	}

	ctx := context.Background()
	pipe := r.rdb.Pipeline()

	ttl := time.Duration(config.GlobalConfig.App.CacheTTL) * time.Second
	if ttl <= 0 {
		ttl = 7 * 24 * time.Hour // 默认7天
	}

	for hash, question := range questions {
		key := model.GenerateCacheKey(hash)
		data, err := json.Marshal(question)
		if err != nil {
			continue // 跳过序列化失败的数据
		}
		pipe.Set(ctx, key, data, ttl)
	}

	_, err := pipe.Exec(ctx)
	return err
}

// BatchGet 批量获取缓存
func (r *QuestionCacheRepository) BatchGet(hashes []string) (map[string]*model.Question, error) {
	if r.rdb == nil {
		return make(map[string]*model.Question), nil // Redis不可用时返回空map
	}

	ctx := context.Background()
	pipe := r.rdb.Pipeline()

	// 构建键列表
	keys := make([]string, len(hashes))
	for i, hash := range hashes {
		keys[i] = model.GenerateCacheKey(hash)
	}

	// 批量获取
	for _, key := range keys {
		pipe.Get(ctx, key)
	}

	results, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return nil, err
	}

	// 解析结果
	questions := make(map[string]*model.Question)
	for i, result := range results {
		if cmd, ok := result.(*redis.StringCmd); ok {
			data, err := cmd.Result()
			if err == nil {
				var question model.Question
				if json.Unmarshal([]byte(data), &question) == nil {
					questions[hashes[i]] = &question
				}
			}
		}
	}

	return questions, nil
}
