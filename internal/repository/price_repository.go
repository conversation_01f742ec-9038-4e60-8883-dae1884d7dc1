package repository

import (
	"errors"
	"solve_api/internal/model"

	"gorm.io/gorm"
)

type PriceConfigRepository struct {
	db *gorm.DB
}

// NewPriceConfigRepository 创建价格配置仓库实例
func NewPriceConfigRepository(db *gorm.DB) *PriceConfigRepository {
	return &PriceConfigRepository{db: db}
}

// GetPrice 获取服务价格（优先用户定制价格，否则使用默认价格）
func (r *PriceConfigRepository) GetPrice(serviceType int, userID uint) (float64, error) {
	var priceConfig model.PriceConfig
	
	// 先查找用户定制价格
	err := r.db.Where("service_type = ? AND user_id = ?", serviceType, userID).First(&priceConfig).Error
	if err == nil {
		return priceConfig.Price, nil
	}

	// 如果没有用户定制价格，查找默认价格
	err = r.db.Where("service_type = ? AND user_id = 0", serviceType).First(&priceConfig).Error
	if err == nil {
		return priceConfig.Price, nil
	}

	// 如果都没有，返回默认价格
	switch serviceType {
	case model.ApplicationTypePhotoSearch:
		return 0.01, nil // 拍照搜题默认0.01元/次
	default:
		return 0.01, nil
	}
}

// GetDefaultPrice 获取默认价格
func (r *PriceConfigRepository) GetDefaultPrice(serviceType int) (float64, error) {
	var priceConfig model.PriceConfig
	err := r.db.Where("service_type = ? AND user_id = 0", serviceType).First(&priceConfig).Error
	if err != nil {
		// 如果没有配置，返回系统默认价格
		switch serviceType {
		case model.ApplicationTypePhotoSearch:
			return 0.01, nil
		default:
			return 0.01, nil
		}
	}
	return priceConfig.Price, nil
}

// SetUserPrice 设置用户定制价格
func (r *PriceConfigRepository) SetUserPrice(serviceType int, userID uint, price float64) error {
	// 先查找是否已存在
	var existing model.PriceConfig
	err := r.db.Where("service_type = ? AND user_id = ?", serviceType, userID).First(&existing).Error
	
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 创建新记录
		priceConfig := &model.PriceConfig{
			ServiceType: serviceType,
			UserID:      userID,
			Price:       price,
		}
		return r.db.Create(priceConfig).Error
	} else {
		// 更新现有记录
		return r.db.Model(&existing).Update("price", price).Error
	}
}

// SetDefaultPrice 设置默认价格
func (r *PriceConfigRepository) SetDefaultPrice(serviceType int, price float64) error {
	// 先查找是否已存在
	var existing model.PriceConfig
	err := r.db.Where("service_type = ? AND user_id = 0", serviceType).First(&existing).Error
	
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 创建新记录
		priceConfig := &model.PriceConfig{
			ServiceType: serviceType,
			UserID:      0, // 0表示默认价格
			Price:       price,
		}
		return r.db.Create(priceConfig).Error
	} else {
		// 更新现有记录
		return r.db.Model(&existing).Update("price", price).Error
	}
}

// GetByID 根据ID获取价格配置
func (r *PriceConfigRepository) GetByID(id uint) (*model.PriceConfig, error) {
	var priceConfig model.PriceConfig
	err := r.db.First(&priceConfig, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &priceConfig, nil
}

// Create 创建价格配置
func (r *PriceConfigRepository) Create(priceConfig *model.PriceConfig) error {
	return r.db.Create(priceConfig).Error
}

// Update 更新价格配置
func (r *PriceConfigRepository) Update(priceConfig *model.PriceConfig) error {
	return r.db.Save(priceConfig).Error
}

// Delete 删除价格配置
func (r *PriceConfigRepository) Delete(id uint) error {
	return r.db.Delete(&model.PriceConfig{}, id).Error
}

// List 获取价格配置列表
func (r *PriceConfigRepository) List(offset, limit int) ([]*model.PriceConfig, int64, error) {
	var priceConfigs []*model.PriceConfig
	var total int64

	// 获取总数
	if err := r.db.Model(&model.PriceConfig{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Offset(offset).Limit(limit).Order("created_at DESC").Find(&priceConfigs).Error
	if err != nil {
		return nil, 0, err
	}

	return priceConfigs, total, nil
}

// GetByServiceType 根据服务类型获取价格配置列表
func (r *PriceConfigRepository) GetByServiceType(serviceType int) ([]*model.PriceConfig, error) {
	var priceConfigs []*model.PriceConfig
	err := r.db.Where("service_type = ?", serviceType).Order("user_id ASC").Find(&priceConfigs).Error
	if err != nil {
		return nil, err
	}
	return priceConfigs, nil
}
