package service

import (
	"fmt"
	"solve_api/internal/model"
	"solve_api/internal/repository"
)

type PriceService struct {
	priceConfigRepo *repository.PriceConfigRepository
}

// NewPriceService 创建价格服务实例
func NewPriceService(priceConfigRepo *repository.PriceConfigRepository) *PriceService {
	return &PriceService{
		priceConfigRepo: priceConfigRepo,
	}
}

// CreatePriceConfig 创建价格配置
func (s *PriceService) CreatePriceConfig(req *model.PriceConfigCreateRequest) (*model.PriceConfigResponse, error) {
	priceConfig := &model.PriceConfig{
		ServiceType: req.ServiceType,
		UserID:      req.UserID,
		Price:       req.Price,
	}

	if err := s.priceConfigRepo.Create(priceConfig); err != nil {
		return nil, fmt.Errorf("创建价格配置失败: %w", err)
	}

	return priceConfig.ToResponse(), nil
}

// GetPriceConfig 获取价格配置详情
func (s *PriceService) GetPriceConfig(id uint) (*model.PriceConfigResponse, error) {
	priceConfig, err := s.priceConfigRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("查询价格配置失败: %w", err)
	}
	if priceConfig == nil {
		return nil, fmt.Errorf("价格配置不存在")
	}

	return priceConfig.ToResponse(), nil
}

// UpdatePriceConfig 更新价格配置
func (s *PriceService) UpdatePriceConfig(id uint, req *model.PriceConfigUpdateRequest) (*model.PriceConfigResponse, error) {
	priceConfig, err := s.priceConfigRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("查询价格配置失败: %w", err)
	}
	if priceConfig == nil {
		return nil, fmt.Errorf("价格配置不存在")
	}

	priceConfig.Price = req.Price

	if err := s.priceConfigRepo.Update(priceConfig); err != nil {
		return nil, fmt.Errorf("更新价格配置失败: %w", err)
	}

	return priceConfig.ToResponse(), nil
}

// DeletePriceConfig 删除价格配置
func (s *PriceService) DeletePriceConfig(id uint) error {
	priceConfig, err := s.priceConfigRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("查询价格配置失败: %w", err)
	}
	if priceConfig == nil {
		return fmt.Errorf("价格配置不存在")
	}

	if err := s.priceConfigRepo.Delete(id); err != nil {
		return fmt.Errorf("删除价格配置失败: %w", err)
	}

	return nil
}

// GetPriceConfigList 获取价格配置列表
func (s *PriceService) GetPriceConfigList(page, pageSize int) ([]*model.PriceConfigResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	priceConfigs, total, err := s.priceConfigRepo.List(offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询价格配置列表失败: %w", err)
	}

	var result []*model.PriceConfigResponse
	for _, priceConfig := range priceConfigs {
		result = append(result, priceConfig.ToResponse())
	}

	return result, total, nil
}

// GetServicePrice 获取服务价格
func (s *PriceService) GetServicePrice(serviceType int, userID uint) (float64, error) {
	return s.priceConfigRepo.GetPrice(serviceType, userID)
}

// SetDefaultPrice 设置默认价格
func (s *PriceService) SetDefaultPrice(serviceType int, price float64) error {
	return s.priceConfigRepo.SetDefaultPrice(serviceType, price)
}

// SetUserPrice 设置用户定制价格
func (s *PriceService) SetUserPrice(serviceType int, userID uint, price float64) error {
	return s.priceConfigRepo.SetUserPrice(serviceType, userID, price)
}

// GetDefaultPrice 获取默认价格
func (s *PriceService) GetDefaultPrice(serviceType int) (float64, error) {
	return s.priceConfigRepo.GetDefaultPrice(serviceType)
}

// GetPriceConfigsByServiceType 根据服务类型获取价格配置列表
func (s *PriceService) GetPriceConfigsByServiceType(serviceType int) ([]*model.PriceConfigResponse, error) {
	priceConfigs, err := s.priceConfigRepo.GetByServiceType(serviceType)
	if err != nil {
		return nil, fmt.Errorf("查询价格配置失败: %w", err)
	}

	var result []*model.PriceConfigResponse
	for _, priceConfig := range priceConfigs {
		result = append(result, priceConfig.ToResponse())
	}

	return result, nil
}
