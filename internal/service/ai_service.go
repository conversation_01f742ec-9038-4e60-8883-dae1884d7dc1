package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"time"
)

type AIService struct {
	modelConfigRepo *repository.ModelConfigRepository
}

// NewAIService 创建AI服务实例
func NewAIService(modelConfigRepo *repository.ModelConfigRepository) *AIService {
	return &AIService{
		modelConfigRepo: modelConfigRepo,
	}
}

// CallQwenVL 调用Qwen-VL模型进行图像识别
func (s *AIService) CallQwenVL(imageURL string) (*model.QuestionStructure, error) {
	// 获取模型配置
	modelConfig, err := s.modelConfigRepo.GetEnabledByName(model.ModelNameQwenVLPlus)
	if err != nil {
		return nil, fmt.Errorf("获取Qwen-VL模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return nil, fmt.Errorf("Qwen-VL模型未配置或未启用")
	}

	// 构建请求参数
	params, err := modelConfig.GetParamsMap()
	if err != nil {
		return nil, fmt.Errorf("解析模型参数失败: %w", err)
	}

	// 构建请求体
	requestBody := map[string]interface{}{
		"model": "qwen-vl-plus",
		"input": map[string]interface{}{
			"messages": []map[string]interface{}{
				{
					"role": "system",
					"content": "你是一个专业的题目识别助手，能够准确识别图片中的题目内容并进行结构化解析。",
				},
				{
					"role": "user",
					"content": []map[string]interface{}{
						{
							"image": imageURL,
						},
						{
							"text": `请识别这张图片中的题目内容，并进行结构化解析。请以JSON格式返回，包含以下字段：
- question_type: 题目类型（如：单选题、多选题、填空题、解答题、判断题等）
- question_text: 题目内容（不包含题目序号和类型标识）
- options: 选项内容（仅选择题有，格式为{"A":"选项A","B":"选项B",...}）
- subject: 学科（如：数学、语文、英语、物理等）
- grade: 年级（如：小学、初中、高中、大学等）
- difficulty: 难度等级（1-5，1最简单，5最困难）
- raw_content: 图片中识别到的原始文本内容

注意：question_text字段中不应包含题目类型标识和序号。`,
						},
					},
				},
			},
		},
		"parameters": params,
	}

	// 发送请求
	response, err := s.sendRequest(modelConfig.ApiURL, modelConfig.ApiKey, requestBody)
	if err != nil {
		return nil, fmt.Errorf("调用Qwen-VL模型失败: %w", err)
	}

	// 解析响应
	var qwenResponse model.QwenVLResponse
	if err := json.Unmarshal(response, &qwenResponse); err != nil {
		return nil, fmt.Errorf("解析Qwen-VL响应失败: %w", err)
	}

	if len(qwenResponse.Output.Choices) == 0 {
		return nil, fmt.Errorf("Qwen-VL模型返回空结果")
	}

	content := qwenResponse.Output.Choices[0].Message.Content

	// 尝试解析JSON格式的响应
	var structure model.QuestionStructure
	if err := json.Unmarshal([]byte(content), &structure); err != nil {
		// 如果不是JSON格式，则作为纯文本处理
		structure = model.QuestionStructure{
			QuestionText: content,
			RawContent:   content,
			Subject:      "未知",
			Grade:        "未知",
			Difficulty:   3, // 默认中等难度
			Content:      content, // 向后兼容
		}
	} else {
		// 确保向后兼容
		if structure.Content == "" && structure.QuestionText != "" {
			structure.Content = structure.QuestionText
		}
		// 如果没有原始内容，使用题目文本
		if structure.RawContent == "" {
			structure.RawContent = structure.QuestionText
		}
	}

	return &structure, nil
}

// CallDeepseek 调用Deepseek模型进行题目解析
func (s *AIService) CallDeepseek(questionContent string) (string, string, error) {
	// 获取模型配置
	modelConfig, err := s.modelConfigRepo.GetEnabledByName(model.ModelNameDeepseekChat)
	if err != nil {
		return "", "", fmt.Errorf("获取Deepseek模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return "", "", fmt.Errorf("Deepseek模型未配置或未启用")
	}

	// 构建请求参数
	params, err := modelConfig.GetParamsMap()
	if err != nil {
		return "", "", fmt.Errorf("解析模型参数失败: %w", err)
	}

	// 构建请求体
	requestBody := map[string]interface{}{
		"model": "deepseek-chat",
		"messages": []map[string]interface{}{
			{
				"role": "user",
				"content": fmt.Sprintf(`请对以下题目进行详细解析，包括解题思路和答案。

题目：%s

请以JSON格式返回，包含以下字段：
- analysis: 详细的解题思路和步骤
- answer: 最终答案

请确保返回的是有效的JSON格式。`, questionContent),
			},
		},
	}

	// 合并参数
	for key, value := range params {
		requestBody[key] = value
	}

	// 发送请求
	response, err := s.sendRequest(modelConfig.ApiURL, modelConfig.ApiKey, requestBody)
	if err != nil {
		return "", "", fmt.Errorf("调用Deepseek模型失败: %w", err)
	}

	// 解析响应
	var deepseekResponse model.DeepseekResponse
	if err := json.Unmarshal(response, &deepseekResponse); err != nil {
		return "", "", fmt.Errorf("解析Deepseek响应失败: %w", err)
	}

	if len(deepseekResponse.Choices) == 0 {
		return "", "", fmt.Errorf("Deepseek模型返回空结果")
	}

	content := deepseekResponse.Choices[0].Message.Content

	// 尝试解析JSON格式的响应
	var result struct {
		Analysis string `json:"analysis"`
		Answer   string `json:"answer"`
	}

	if err := json.Unmarshal([]byte(content), &result); err != nil {
		// 如果不是JSON格式，则作为纯文本处理
		return content, "请参考解析内容", nil
	}

	return result.Analysis, result.Answer, nil
}

// sendRequest 发送HTTP请求
func (s *AIService) sendRequest(apiURL, apiKey string, requestBody map[string]interface{}) ([]byte, error) {
	// 序列化请求体
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+apiKey)

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	return body, nil
}

// ValidateImageURL 验证图片URL是否可访问
func (s *AIService) ValidateImageURL(imageURL string) error {
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Head(imageURL)
	if err != nil {
		return fmt.Errorf("无法访问图片URL: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("图片URL返回错误状态码: %d", resp.StatusCode)
	}

	// 检查Content-Type是否为图片
	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		return fmt.Errorf("无法确定文件类型")
	}

	validTypes := []string{
		"image/jpeg",
		"image/jpg", 
		"image/png",
		"image/gif",
		"image/bmp",
		"image/webp",
	}

	for _, validType := range validTypes {
		if contentType == validType {
			return nil
		}
	}

	return fmt.Errorf("不支持的图片格式: %s", contentType)
}
