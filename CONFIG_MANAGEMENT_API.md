# 配置管理API接口文档

## 📋 接口概述

本文档专门为前端开发团队提供配置管理相关API的完整接入指南，包括系统配置、模型配置、价格配置等管理功能。

## 🔗 基础信息

- **基础URL**: `http://localhost:8080/api/v1/admin`
- **认证方式**: Bearer <PERSON>（管理员权限）
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 🔐 认证说明

所有配置管理API都需要管理员权限，请在请求头中携带管理员token：

```http
Authorization: Bearer admin_token
Content-Type: application/json
```

## 🛠️ 系统配置管理

### 数据结构

#### SystemConfig 系统配置
```typescript
interface SystemConfig {
  id: number;
  key: string;            // 配置键名
  value: string;          // 配置值
  description: string;    // 配置描述
  created_at: string;
  updated_at: string;
}
```

#### 系统配置键常量
```typescript
const CONFIG_KEYS = {
  INVITE_CODE: 'invite_code',    // 邀请码
  RATE_LIMIT: 'rate_limit',      // 限流配置（次/秒）
  CACHE_TTL: 'cache_ttl'         // 缓存TTL（秒）
};
```

### API接口

#### 1. 获取系统信息（包含配置）
```http
GET /api/v1/admin/system/info
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取系统信息成功",
  "data": {
    "system_stats": {
      "total_users": 150,
      "total_apps": 45,
      "total_calls": 12580
    },
    "configs": {
      "invite_code": "SOLVE2024",
      "rate_limit": "10",
      "cache_ttl": "604800"
    },
    "version": "1.0.0",
    "build_time": "2024-01-01"
  }
}
```

#### 2. 批量更新系统配置
```http
PUT /api/v1/admin/system/config
```

**请求参数**:
```json
{
  "invite_code": "SOLVE2025",
  "rate_limit": "20",
  "cache_ttl": "86400"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "系统配置更新成功",
  "data": null
}
```

## 🤖 模型配置管理

### 数据结构

#### ModelConfig 模型配置
```typescript
interface ModelConfig {
  id: number;
  name: string;           // 模型名称，如 "qwen-vl-plus"
  api_url: string;        // API地址
  api_key: string;        // API密钥
  params: object;         // 参数配置对象
  status: number;         // 1=启用, 2=禁用
  status_name: string;    // 状态名称
  created_at: string;
  updated_at: string;
}
```

#### 模型参数配置示例
```json
{
  "temperature": 0.3,
  "max_tokens": 1500,
  "top_p": 0.8,
  "response_format": {"type": "json_object"},
  "detail": "high"
}
```

### API接口

#### 1. 创建模型配置
```http
POST /api/v1/admin/model
```

**请求参数**:
```json
{
  "name": "qwen-vl-plus",
  "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
  "api_key": "sk-xxx",
  "params": {
    "temperature": 0.3,
    "max_tokens": 1500,
    "top_p": 0.8,
    "response_format": {"type": "json_object"},
    "detail": "high"
  },
  "status": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "模型配置创建成功",
  "data": {
    "id": 1,
    "name": "qwen-vl-plus",
    "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
    "api_key": "sk-xxx",
    "params": {
      "temperature": 0.3,
      "max_tokens": 1500,
      "top_p": 0.8,
      "response_format": {"type": "json_object"},
      "detail": "high"
    },
    "status": 1,
    "status_name": "启用",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

#### 2. 获取模型配置列表
```http
GET /api/v1/admin/model?page=1&page_size=10
```

**查询参数**:
| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| page | number | 否 | 页码 | 1 |
| page_size | number | 否 | 每页数量 | 10 |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取模型配置列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "qwen-vl-plus",
        "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
        "status": 1,
        "status_name": "启用",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10
  }
}
```

#### 3. 获取启用的模型配置
```http
GET /api/v1/admin/model/enabled
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取启用的模型配置成功",
  "data": [
    {
      "id": 1,
      "name": "qwen-vl-plus",
      "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
      "status": 1,
      "status_name": "启用",
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

#### 4. 获取模型配置详情
```http
GET /api/v1/admin/model/{id}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 模型配置ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取模型配置成功",
  "data": {
    "id": 1,
    "name": "qwen-vl-plus",
    "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
    "api_key": "sk-xxx",
    "params": {
      "temperature": 0.3,
      "max_tokens": 1500,
      "top_p": 0.8,
      "response_format": {"type": "json_object"},
      "detail": "high"
    },
    "status": 1,
    "status_name": "启用",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

#### 5. 更新模型配置
```http
PUT /api/v1/admin/model/{id}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 模型配置ID |

**请求参数**（所有字段都是可选的）:
```json
{
  "name": "qwen-vl-plus-v2",
  "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
  "api_key": "sk-new-key",
  "params": {
    "temperature": 0.5,
    "max_tokens": 2000
  },
  "status": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "模型配置更新成功",
  "data": {
    "id": 1,
    "name": "qwen-vl-plus-v2",
    "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
    "api_key": "sk-new-key",
    "params": {
      "temperature": 0.5,
      "max_tokens": 2000,
      "top_p": 0.8,
      "response_format": {"type": "json_object"},
      "detail": "high"
    },
    "status": 1,
    "status_name": "启用",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T11:00:00Z"
  }
}
```

#### 6. 更新模型状态
```http
PUT /api/v1/admin/model/{id}/status
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 模型配置ID |

**请求参数**:
```json
{
  "status": 2
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "模型状态更新成功",
  "data": null
}
```

#### 7. 删除模型配置
```http
DELETE /api/v1/admin/model/{id}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 模型配置ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "模型配置删除成功",
  "data": null
}
```

## 💰 价格配置管理

### 数据结构

#### PriceConfig 价格配置
```typescript
interface PriceConfig {
  id: number;
  service_type: number;   // 服务类型：1=拍照搜题
  service_name: string;   // 服务名称
  price: number;          // 价格（元）
  user_id?: number;       // 用户ID（用户专属价格，null为默认价格）
  is_default: boolean;    // 是否为默认价格
  status: number;         // 1=启用, 2=禁用
  status_name: string;    // 状态名称
  created_at: string;
  updated_at: string;
}
```

#### 服务类型常量
```typescript
const SERVICE_TYPES = {
  PHOTO_SEARCH: 1    // 拍照搜题服务
};
```

### API接口

#### 1. 创建价格配置
```http
POST /api/v1/admin/price
```

**请求参数**:
```json
{
  "service_type": 1,
  "price": 0.1,
  "user_id": null,
  "status": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "价格配置创建成功",
  "data": {
    "id": 1,
    "service_type": 1,
    "service_name": "拍照搜题",
    "price": 0.1,
    "user_id": null,
    "is_default": true,
    "status": 1,
    "status_name": "启用",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

#### 2. 获取价格配置列表
```http
GET /api/v1/admin/price?page=1&page_size=10
```

**查询参数**:
| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| page | number | 否 | 页码 | 1 |
| page_size | number | 否 | 每页数量 | 10 |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取价格配置列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "service_type": 1,
        "service_name": "拍照搜题",
        "price": 0.1,
        "user_id": null,
        "is_default": true,
        "status": 1,
        "status_name": "启用",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10
  }
}
```

#### 3. 获取价格配置详情
```http
GET /api/v1/admin/price/{id}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 价格配置ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取价格配置成功",
  "data": {
    "id": 1,
    "service_type": 1,
    "service_name": "拍照搜题",
    "price": 0.1,
    "user_id": null,
    "is_default": true,
    "status": 1,
    "status_name": "启用",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

#### 4. 更新价格配置
```http
PUT /api/v1/admin/price/{id}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 价格配置ID |

**请求参数**（所有字段都是可选的）:
```json
{
  "service_type": 1,
  "price": 0.15,
  "status": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "价格配置更新成功",
  "data": {
    "id": 1,
    "service_type": 1,
    "service_name": "拍照搜题",
    "price": 0.15,
    "user_id": null,
    "is_default": true,
    "status": 1,
    "status_name": "启用",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T11:00:00Z"
  }
}
```

#### 5. 删除价格配置
```http
DELETE /api/v1/admin/price/{id}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 价格配置ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "价格配置删除成功",
  "data": null
}
```

#### 6. 根据服务类型获取价格
```http
GET /api/v1/admin/price/service/{service_type}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| service_type | number | 是 | 服务类型 |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取服务价格成功",
  "data": {
    "service_type": 1,
    "service_name": "拍照搜题",
    "default_price": 0.1,
    "user_prices": [
      {
        "user_id": 123,
        "price": 0.08,
        "username": "test_user"
      }
    ]
  }
}
```

#### 7. 设置默认价格
```http
PUT /api/v1/admin/price/service/{service_type}/default
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| service_type | number | 是 | 服务类型 |

**请求参数**:
```json
{
  "price": 0.12
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "默认价格设置成功",
  "data": null
}
```

#### 8. 设置用户专属价格
```http
PUT /api/v1/admin/price/service/{service_type}/user/{user_id}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| service_type | number | 是 | 服务类型 |
| user_id | number | 是 | 用户ID |

**请求参数**:
```json
{
  "price": 0.08
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "用户专属价格设置成功",
  "data": null
}
```

## ❌ 错误响应

### 错误响应格式
```json
{
  "code": 错误码,
  "message": "错误描述",
  "data": null
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 错误描述 | 解决方案 |
|--------|------------|----------|----------|
| 400 | 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 401 | 401 | 未授权访问 | 检查管理员token是否有效 |
| 403 | 403 | 权限不足 | 确认当前用户具有管理员权限 |
| 404 | 404 | 资源不存在 | 检查配置ID是否正确 |
| 409 | 409 | 资源冲突 | 检查是否存在重复的配置名称 |
| 500 | 500 | 服务器内部错误 | 稍后重试或联系技术支持 |

### 错误响应示例

#### 401 未授权
```json
{
  "code": 401,
  "message": "未授权访问",
  "data": null
}
```

#### 404 配置不存在
```json
{
  "code": 404,
  "message": "模型配置不存在",
  "data": null
}
```

#### 409 名称冲突
```json
{
  "code": 409,
  "message": "模型名称已存在",
  "data": null
}
```

## 🚀 调用示例

### JavaScript/Fetch

#### 获取模型配置列表
```javascript
const getModelConfigs = async (page = 1, pageSize = 10) => {
  try {
    const response = await fetch(`http://localhost:8080/api/v1/admin/model?page=${page}&page_size=${pageSize}`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + adminToken,
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();

    if (result.code === 200) {
      console.log('模型配置列表:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取模型配置失败:', error);
    throw error;
  }
};
```

#### 创建模型配置
```javascript
const createModelConfig = async (modelData) => {
  try {
    const response = await fetch('http://localhost:8080/api/v1/admin/model', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + adminToken,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(modelData)
    });

    const result = await response.json();

    if (result.code === 200) {
      console.log('模型配置创建成功:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('创建模型配置失败:', error);
    throw error;
  }
};

// 使用示例
createModelConfig({
  name: 'qwen-vl-plus',
  api_url: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation',
  api_key: 'sk-xxx',
  params: {
    temperature: 0.3,
    max_tokens: 1500,
    top_p: 0.8,
    response_format: {type: 'json_object'},
    detail: 'high'
  },
  status: 1
});
```

#### 更新系统配置
```javascript
const updateSystemConfig = async (configs) => {
  try {
    const response = await fetch('http://localhost:8080/api/v1/admin/system/config', {
      method: 'PUT',
      headers: {
        'Authorization': 'Bearer ' + adminToken,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(configs)
    });

    const result = await response.json();

    if (result.code === 200) {
      console.log('系统配置更新成功');
      return true;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('更新系统配置失败:', error);
    throw error;
  }
};

// 使用示例
updateSystemConfig({
  invite_code: 'SOLVE2025',
  rate_limit: '20',
  cache_ttl: '86400'
});
```

### Python requests

```python
import requests
import json

class ConfigAPI:
    def __init__(self, base_url, admin_token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {admin_token}',
            'Content-Type': 'application/json'
        }

    def get_model_configs(self, page=1, page_size=10):
        """获取模型配置列表"""
        url = f"{self.base_url}/admin/model"
        params = {'page': page, 'page_size': page_size}

        try:
            response = requests.get(url, headers=self.headers, params=params)
            result = response.json()

            if result['code'] == 200:
                return result['data']
            else:
                raise Exception(result['message'])

        except requests.exceptions.RequestException as e:
            raise Exception(f"请求失败: {e}")

    def create_model_config(self, model_data):
        """创建模型配置"""
        url = f"{self.base_url}/admin/model"

        try:
            response = requests.post(url, headers=self.headers, json=model_data)
            result = response.json()

            if result['code'] == 200:
                return result['data']
            else:
                raise Exception(result['message'])

        except requests.exceptions.RequestException as e:
            raise Exception(f"请求失败: {e}")

    def update_system_config(self, configs):
        """更新系统配置"""
        url = f"{self.base_url}/admin/system/config"

        try:
            response = requests.put(url, headers=self.headers, json=configs)
            result = response.json()

            if result['code'] == 200:
                return True
            else:
                raise Exception(result['message'])

        except requests.exceptions.RequestException as e:
            raise Exception(f"请求失败: {e}")

# 使用示例
api = ConfigAPI('http://localhost:8080/api/v1', 'your_admin_token')

try:
    # 获取模型配置列表
    models = api.get_model_configs(page=1, page_size=10)
    print(f"模型配置数量: {models['total']}")

    # 创建新的模型配置
    new_model = api.create_model_config({
        'name': 'deepseek-chat',
        'api_url': 'https://api.deepseek.com/v1/chat/completions',
        'api_key': 'sk-xxx',
        'params': {
            'temperature': 0.3,
            'max_tokens': 2500,
            'top_p': 0.8,
            'response_format': {'type': 'json_object'}
        },
        'status': 1
    })
    print(f"新模型配置ID: {new_model['id']}")

    # 更新系统配置
    api.update_system_config({
        'invite_code': 'SOLVE2025',
        'rate_limit': '20'
    })
    print("系统配置更新成功")

except Exception as e:
    print(f"操作失败: {e}")
```

### curl 命令

#### 获取模型配置列表
```bash
curl -X GET "http://localhost:8080/api/v1/admin/model?page=1&page_size=10" \
  -H "Authorization: Bearer your_admin_token" \
  -H "Content-Type: application/json"
```

#### 创建模型配置
```bash
curl -X POST http://localhost:8080/api/v1/admin/model \
  -H "Authorization: Bearer your_admin_token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "qwen-vl-plus",
    "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
    "api_key": "sk-xxx",
    "params": {
      "temperature": 0.3,
      "max_tokens": 1500,
      "top_p": 0.8,
      "response_format": {"type": "json_object"},
      "detail": "high"
    },
    "status": 1
  }'
```

#### 更新系统配置
```bash
curl -X PUT http://localhost:8080/api/v1/admin/system/config \
  -H "Authorization: Bearer your_admin_token" \
  -H "Content-Type: application/json" \
  -d '{
    "invite_code": "SOLVE2025",
    "rate_limit": "20",
    "cache_ttl": "86400"
  }'
```

## 📞 技术支持

### 常见问题

1. **Q**: 如何获取管理员token？
   **A**: 需要先通过管理员登录接口获取token

2. **Q**: 模型参数配置有什么限制？
   **A**: 参数必须是有效的JSON格式，具体参数取决于对应的AI模型

3. **Q**: 价格配置的精度是多少？
   **A**: 支持到小数点后4位，最小单位0.0001元

4. **Q**: 系统配置更新后是否立即生效？
   **A**: 是的，配置更新后立即生效，无需重启服务

### 联系方式
- 📧 技术支持: <EMAIL>
- 📱 客服电话: 400-123-4567
- 💬 在线客服: 工作日 9:00-18:00

---

**注意**: 配置管理功能仅限管理员使用，请妥善保管管理员凭证。生产环境请使用HTTPS协议确保数据传输安全。
